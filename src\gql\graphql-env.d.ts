/* eslint-disable */
/* prettier-ignore */

export type introspection_types = {
    'AddFulfillmentToOrderResult': { kind: 'UNION'; name: 'AddFulfillmentToOrderResult'; fields: {}; possibleTypes: 'CreateFulfillmentError' | 'EmptyOrderLineSelectionError' | 'Fulfillment' | 'FulfillmentStateTransitionError' | 'InsufficientStockOnHandError' | 'InvalidFulfillmentHandlerError' | 'ItemsAlreadyFulfilledError'; };
    'AddItemInput': { kind: 'INPUT_OBJECT'; name: 'AddItemInput'; isOneOf: false; inputFields: [{ name: 'productVariantId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'quantity'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'INPUT_OBJECT'; name: 'OrderLineCustomFieldsInput'; ofType: null; }; defaultValue: null }]; };
    'AddItemToDraftOrderInput': { kind: 'INPUT_OBJECT'; name: 'AddItemToDraftOrderInput'; isOneOf: false; inputFields: [{ name: 'productVariantId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'quantity'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'INPUT_OBJECT'; name: 'OrderLineCustomFieldsInput'; ofType: null; }; defaultValue: null }]; };
    'AddManualPaymentToOrderResult': { kind: 'UNION'; name: 'AddManualPaymentToOrderResult'; fields: {}; possibleTypes: 'ManualPaymentStateError' | 'Order'; };
    'AddNoteToCustomerInput': { kind: 'INPUT_OBJECT'; name: 'AddNoteToCustomerInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'note'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'isPublic'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; }; defaultValue: null }]; };
    'AddNoteToOrderInput': { kind: 'INPUT_OBJECT'; name: 'AddNoteToOrderInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'note'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'isPublic'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; }; defaultValue: null }]; };
    'Address': { kind: 'OBJECT'; name: 'Address'; fields: { 'city': { name: 'city'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'company': { name: 'company'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'country': { name: 'country'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Country'; ofType: null; }; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'defaultBillingAddress': { name: 'defaultBillingAddress'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'defaultShippingAddress': { name: 'defaultShippingAddress'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'fullName': { name: 'fullName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'phoneNumber': { name: 'phoneNumber'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'postalCode': { name: 'postalCode'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'province': { name: 'province'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'streetLine1': { name: 'streetLine1'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'streetLine2': { name: 'streetLine2'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'AdjustDraftOrderLineInput': { kind: 'INPUT_OBJECT'; name: 'AdjustDraftOrderLineInput'; isOneOf: false; inputFields: [{ name: 'orderLineId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'quantity'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'INPUT_OBJECT'; name: 'OrderLineCustomFieldsInput'; ofType: null; }; defaultValue: null }]; };
    'Adjustment': { kind: 'OBJECT'; name: 'Adjustment'; fields: { 'adjustmentSource': { name: 'adjustmentSource'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'amount': { name: 'amount'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'data': { name: 'data'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'AdjustmentType'; ofType: null; }; } }; }; };
    'AdjustmentType': { name: 'AdjustmentType'; enumValues: 'PROMOTION' | 'DISTRIBUTED_ORDER_PROMOTION' | 'OTHER'; };
    'Administrator': { kind: 'OBJECT'; name: 'Administrator'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'emailAddress': { name: 'emailAddress'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'firstName': { name: 'firstName'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'lastName': { name: 'lastName'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'user': { name: 'user'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'User'; ofType: null; }; } }; }; };
    'AdministratorFilterParameter': { kind: 'INPUT_OBJECT'; name: 'AdministratorFilterParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'firstName'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'lastName'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'emailAddress'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'AdministratorFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'AdministratorFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'AdministratorList': { kind: 'OBJECT'; name: 'AdministratorList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Administrator'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'AdministratorListOptions': { kind: 'INPUT_OBJECT'; name: 'AdministratorListOptions'; isOneOf: false; inputFields: [{ name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'AdministratorSortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'AdministratorFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'AdministratorPaymentInput': { kind: 'INPUT_OBJECT'; name: 'AdministratorPaymentInput'; isOneOf: false; inputFields: [{ name: 'paymentMethod'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'metadata'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'AdministratorRefundInput': { kind: 'INPUT_OBJECT'; name: 'AdministratorRefundInput'; isOneOf: false; inputFields: [{ name: 'paymentId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'reason'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'amount'; type: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; defaultValue: null }]; };
    'AdministratorSortParameter': { kind: 'INPUT_OBJECT'; name: 'AdministratorSortParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'firstName'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'lastName'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'emailAddress'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'Allocation': { kind: 'OBJECT'; name: 'Allocation'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'orderLine': { name: 'orderLine'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'OrderLine'; ofType: null; }; } }; 'productVariant': { name: 'productVariant'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductVariant'; ofType: null; }; } }; 'quantity': { name: 'quantity'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'StockMovementType'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'AlreadyRefundedError': { kind: 'OBJECT'; name: 'AlreadyRefundedError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'refundId': { name: 'refundId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; }; };
    'ApplyCouponCodeResult': { kind: 'UNION'; name: 'ApplyCouponCodeResult'; fields: {}; possibleTypes: 'CouponCodeExpiredError' | 'CouponCodeInvalidError' | 'CouponCodeLimitError' | 'Order'; };
    'Asset': { kind: 'OBJECT'; name: 'Asset'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'fileSize': { name: 'fileSize'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'focalPoint': { name: 'focalPoint'; type: { kind: 'OBJECT'; name: 'Coordinate'; ofType: null; } }; 'height': { name: 'height'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'mimeType': { name: 'mimeType'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'preview': { name: 'preview'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'source': { name: 'source'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'tags': { name: 'tags'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Tag'; ofType: null; }; }; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'AssetType'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'width': { name: 'width'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'AssetFilterParameter': { kind: 'INPUT_OBJECT'; name: 'AssetFilterParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'type'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'fileSize'; type: { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; ofType: null; }; defaultValue: null }, { name: 'mimeType'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'width'; type: { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; ofType: null; }; defaultValue: null }, { name: 'height'; type: { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; ofType: null; }; defaultValue: null }, { name: 'source'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'preview'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'AssetFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'AssetFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'AssetList': { kind: 'OBJECT'; name: 'AssetList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Asset'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'AssetListOptions': { kind: 'INPUT_OBJECT'; name: 'AssetListOptions'; isOneOf: false; inputFields: [{ name: 'tags'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; }; defaultValue: null }, { name: 'tagsOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }, { name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'AssetSortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'AssetFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'AssetSortParameter': { kind: 'INPUT_OBJECT'; name: 'AssetSortParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'fileSize'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'mimeType'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'width'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'height'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'source'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'preview'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'AssetType': { name: 'AssetType'; enumValues: 'IMAGE' | 'VIDEO' | 'BINARY'; };
    'AssignAssetsToChannelInput': { kind: 'INPUT_OBJECT'; name: 'AssignAssetsToChannelInput'; isOneOf: false; inputFields: [{ name: 'assetIds'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'channelId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }]; };
    'AssignCollectionsToChannelInput': { kind: 'INPUT_OBJECT'; name: 'AssignCollectionsToChannelInput'; isOneOf: false; inputFields: [{ name: 'collectionIds'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'channelId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }]; };
    'AssignFacetsToChannelInput': { kind: 'INPUT_OBJECT'; name: 'AssignFacetsToChannelInput'; isOneOf: false; inputFields: [{ name: 'facetIds'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'channelId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }]; };
    'AssignPaymentMethodsToChannelInput': { kind: 'INPUT_OBJECT'; name: 'AssignPaymentMethodsToChannelInput'; isOneOf: false; inputFields: [{ name: 'paymentMethodIds'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'channelId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }]; };
    'AssignProductVariantsToChannelInput': { kind: 'INPUT_OBJECT'; name: 'AssignProductVariantsToChannelInput'; isOneOf: false; inputFields: [{ name: 'productVariantIds'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'channelId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'priceFactor'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }]; };
    'AssignProductsToChannelInput': { kind: 'INPUT_OBJECT'; name: 'AssignProductsToChannelInput'; isOneOf: false; inputFields: [{ name: 'productIds'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'channelId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'priceFactor'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }]; };
    'AssignPromotionsToChannelInput': { kind: 'INPUT_OBJECT'; name: 'AssignPromotionsToChannelInput'; isOneOf: false; inputFields: [{ name: 'promotionIds'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'channelId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }]; };
    'AssignShippingMethodsToChannelInput': { kind: 'INPUT_OBJECT'; name: 'AssignShippingMethodsToChannelInput'; isOneOf: false; inputFields: [{ name: 'shippingMethodIds'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'channelId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }]; };
    'AssignStockLocationsToChannelInput': { kind: 'INPUT_OBJECT'; name: 'AssignStockLocationsToChannelInput'; isOneOf: false; inputFields: [{ name: 'stockLocationIds'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'channelId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }]; };
    'AuthenticationInput': { kind: 'INPUT_OBJECT'; name: 'AuthenticationInput'; isOneOf: false; inputFields: [{ name: 'native'; type: { kind: 'INPUT_OBJECT'; name: 'NativeAuthInput'; ofType: null; }; defaultValue: null }]; };
    'AuthenticationMethod': { kind: 'OBJECT'; name: 'AuthenticationMethod'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'strategy': { name: 'strategy'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'AuthenticationResult': { kind: 'UNION'; name: 'AuthenticationResult'; fields: {}; possibleTypes: 'CurrentUser' | 'InvalidCredentialsError'; };
    'Boolean': unknown;
    'BooleanCustomFieldConfig': { kind: 'OBJECT'; name: 'BooleanCustomFieldConfig'; fields: { 'description': { name: 'description'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'internal': { name: 'internal'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'list': { name: 'list'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'nullable': { name: 'nullable'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'readonly': { name: 'readonly'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiresPermission': { name: 'requiresPermission'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'ui': { name: 'ui'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; };
    'BooleanListOperators': { kind: 'INPUT_OBJECT'; name: 'BooleanListOperators'; isOneOf: false; inputFields: [{ name: 'inList'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; }; defaultValue: null }]; };
    'BooleanOperators': { kind: 'INPUT_OBJECT'; name: 'BooleanOperators'; isOneOf: false; inputFields: [{ name: 'eq'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'isNull'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }]; };
    'BooleanStructFieldConfig': { kind: 'OBJECT'; name: 'BooleanStructFieldConfig'; fields: { 'description': { name: 'description'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'label': { name: 'label'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'list': { name: 'list'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'ui': { name: 'ui'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; };
    'CancelActiveOrderError': { kind: 'OBJECT'; name: 'CancelActiveOrderError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'orderState': { name: 'orderState'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'CancelOrderInput': { kind: 'INPUT_OBJECT'; name: 'CancelOrderInput'; isOneOf: false; inputFields: [{ name: 'orderId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'lines'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'OrderLineInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'cancelShipping'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'reason'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'CancelOrderResult': { kind: 'UNION'; name: 'CancelOrderResult'; fields: {}; possibleTypes: 'CancelActiveOrderError' | 'EmptyOrderLineSelectionError' | 'MultipleOrderError' | 'Order' | 'OrderStateTransitionError' | 'QuantityTooGreatError'; };
    'CancelPaymentError': { kind: 'OBJECT'; name: 'CancelPaymentError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'paymentErrorMessage': { name: 'paymentErrorMessage'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'CancelPaymentResult': { kind: 'UNION'; name: 'CancelPaymentResult'; fields: {}; possibleTypes: 'CancelPaymentError' | 'Payment' | 'PaymentStateTransitionError'; };
    'Cancellation': { kind: 'OBJECT'; name: 'Cancellation'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'orderLine': { name: 'orderLine'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'OrderLine'; ofType: null; }; } }; 'productVariant': { name: 'productVariant'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductVariant'; ofType: null; }; } }; 'quantity': { name: 'quantity'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'StockMovementType'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'Channel': { kind: 'OBJECT'; name: 'Channel'; fields: { 'availableCurrencyCodes': { name: 'availableCurrencyCodes'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'CurrencyCode'; ofType: null; }; }; }; } }; 'availableLanguageCodes': { name: 'availableLanguageCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; }; } }; 'code': { name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'currencyCode': { name: 'currencyCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'CurrencyCode'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'defaultCurrencyCode': { name: 'defaultCurrencyCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'CurrencyCode'; ofType: null; }; } }; 'defaultLanguageCode': { name: 'defaultLanguageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; } }; 'defaultShippingZone': { name: 'defaultShippingZone'; type: { kind: 'OBJECT'; name: 'Zone'; ofType: null; } }; 'defaultTaxZone': { name: 'defaultTaxZone'; type: { kind: 'OBJECT'; name: 'Zone'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'outOfStockThreshold': { name: 'outOfStockThreshold'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'pricesIncludeTax': { name: 'pricesIncludeTax'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'seller': { name: 'seller'; type: { kind: 'OBJECT'; name: 'Seller'; ofType: null; } }; 'token': { name: 'token'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'trackInventory': { name: 'trackInventory'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'ChannelDefaultLanguageError': { kind: 'OBJECT'; name: 'ChannelDefaultLanguageError'; fields: { 'channelCode': { name: 'channelCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'language': { name: 'language'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'ChannelFilterParameter': { kind: 'INPUT_OBJECT'; name: 'ChannelFilterParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'code'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'token'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'defaultLanguageCode'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'currencyCode'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'defaultCurrencyCode'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'trackInventory'; type: { kind: 'INPUT_OBJECT'; name: 'BooleanOperators'; ofType: null; }; defaultValue: null }, { name: 'outOfStockThreshold'; type: { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; ofType: null; }; defaultValue: null }, { name: 'pricesIncludeTax'; type: { kind: 'INPUT_OBJECT'; name: 'BooleanOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ChannelFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ChannelFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'ChannelList': { kind: 'OBJECT'; name: 'ChannelList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Channel'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'ChannelListOptions': { kind: 'INPUT_OBJECT'; name: 'ChannelListOptions'; isOneOf: false; inputFields: [{ name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'ChannelSortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'ChannelFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'ChannelSortParameter': { kind: 'INPUT_OBJECT'; name: 'ChannelSortParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'code'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'token'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'outOfStockThreshold'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'Collection': { kind: 'OBJECT'; name: 'Collection'; fields: { 'assets': { name: 'assets'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Asset'; ofType: null; }; }; }; } }; 'breadcrumbs': { name: 'breadcrumbs'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'CollectionBreadcrumb'; ofType: null; }; }; }; } }; 'children': { name: 'children'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Collection'; ofType: null; }; }; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'featuredAsset': { name: 'featuredAsset'; type: { kind: 'OBJECT'; name: 'Asset'; ofType: null; } }; 'filters': { name: 'filters'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ConfigurableOperation'; ofType: null; }; }; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'inheritFilters': { name: 'inheritFilters'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'isPrivate': { name: 'isPrivate'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'languageCode': { name: 'languageCode'; type: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'parent': { name: 'parent'; type: { kind: 'OBJECT'; name: 'Collection'; ofType: null; } }; 'parentId': { name: 'parentId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'position': { name: 'position'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'productVariants': { name: 'productVariants'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductVariantList'; ofType: null; }; } }; 'slug': { name: 'slug'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'translations': { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'CollectionTranslation'; ofType: null; }; }; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'CollectionBreadcrumb': { kind: 'OBJECT'; name: 'CollectionBreadcrumb'; fields: { 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'slug': { name: 'slug'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'CollectionFilterParameter': { kind: 'INPUT_OBJECT'; name: 'CollectionFilterParameter'; isOneOf: false; inputFields: [{ name: 'isPrivate'; type: { kind: 'INPUT_OBJECT'; name: 'BooleanOperators'; ofType: null; }; defaultValue: null }, { name: 'inheritFilters'; type: { kind: 'INPUT_OBJECT'; name: 'BooleanOperators'; ofType: null; }; defaultValue: null }, { name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'languageCode'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'slug'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'position'; type: { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; ofType: null; }; defaultValue: null }, { name: 'description'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'parentId'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'CollectionFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'CollectionFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'CollectionList': { kind: 'OBJECT'; name: 'CollectionList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Collection'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'CollectionListOptions': { kind: 'INPUT_OBJECT'; name: 'CollectionListOptions'; isOneOf: false; inputFields: [{ name: 'topLevelOnly'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'CollectionSortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'CollectionFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'CollectionResult': { kind: 'OBJECT'; name: 'CollectionResult'; fields: { 'collection': { name: 'collection'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Collection'; ofType: null; }; } }; 'count': { name: 'count'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'CollectionSortParameter': { kind: 'INPUT_OBJECT'; name: 'CollectionSortParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'slug'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'position'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'description'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'parentId'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'CollectionTranslation': { kind: 'OBJECT'; name: 'CollectionTranslation'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'description': { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'languageCode': { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'slug': { name: 'slug'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'ConfigArg': { kind: 'OBJECT'; name: 'ConfigArg'; fields: { 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'value': { name: 'value'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'ConfigArgDefinition': { kind: 'OBJECT'; name: 'ConfigArgDefinition'; fields: { 'defaultValue': { name: 'defaultValue'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'list': { name: 'list'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'required': { name: 'required'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'ui': { name: 'ui'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; };
    'ConfigArgInput': { kind: 'INPUT_OBJECT'; name: 'ConfigArgInput'; isOneOf: false; inputFields: [{ name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'value'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }]; };
    'ConfigurableOperation': { kind: 'OBJECT'; name: 'ConfigurableOperation'; fields: { 'args': { name: 'args'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ConfigArg'; ofType: null; }; }; }; } }; 'code': { name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'ConfigurableOperationDefinition': { kind: 'OBJECT'; name: 'ConfigurableOperationDefinition'; fields: { 'args': { name: 'args'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ConfigArgDefinition'; ofType: null; }; }; }; } }; 'code': { name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'description': { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'ConfigurableOperationInput': { kind: 'INPUT_OBJECT'; name: 'ConfigurableOperationInput'; isOneOf: false; inputFields: [{ name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'arguments'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ConfigArgInput'; ofType: null; }; }; }; }; defaultValue: null }]; };
    'Coordinate': { kind: 'OBJECT'; name: 'Coordinate'; fields: { 'x': { name: 'x'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; } }; 'y': { name: 'y'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; } }; }; };
    'CoordinateInput': { kind: 'INPUT_OBJECT'; name: 'CoordinateInput'; isOneOf: false; inputFields: [{ name: 'x'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; }; defaultValue: null }, { name: 'y'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; }; defaultValue: null }]; };
    'Country': { kind: 'OBJECT'; name: 'Country'; fields: { 'code': { name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'enabled': { name: 'enabled'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'languageCode': { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'parent': { name: 'parent'; type: { kind: 'INTERFACE'; name: 'Region'; ofType: null; } }; 'parentId': { name: 'parentId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'translations': { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'RegionTranslation'; ofType: null; }; }; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'CountryFilterParameter': { kind: 'INPUT_OBJECT'; name: 'CountryFilterParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'languageCode'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'code'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'type'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'enabled'; type: { kind: 'INPUT_OBJECT'; name: 'BooleanOperators'; ofType: null; }; defaultValue: null }, { name: 'parentId'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'CountryFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'CountryFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'CountryList': { kind: 'OBJECT'; name: 'CountryList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Country'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'CountryListOptions': { kind: 'INPUT_OBJECT'; name: 'CountryListOptions'; isOneOf: false; inputFields: [{ name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'CountrySortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'CountryFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'CountrySortParameter': { kind: 'INPUT_OBJECT'; name: 'CountrySortParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'code'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'type'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'parentId'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'CountryTranslationInput': { kind: 'INPUT_OBJECT'; name: 'CountryTranslationInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CouponCodeExpiredError': { kind: 'OBJECT'; name: 'CouponCodeExpiredError'; fields: { 'couponCode': { name: 'couponCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'CouponCodeInvalidError': { kind: 'OBJECT'; name: 'CouponCodeInvalidError'; fields: { 'couponCode': { name: 'couponCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'CouponCodeLimitError': { kind: 'OBJECT'; name: 'CouponCodeLimitError'; fields: { 'couponCode': { name: 'couponCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'limit': { name: 'limit'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'CreateAddressInput': { kind: 'INPUT_OBJECT'; name: 'CreateAddressInput'; isOneOf: false; inputFields: [{ name: 'fullName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'company'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'streetLine1'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'streetLine2'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'city'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'province'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'postalCode'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'countryCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'phoneNumber'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'defaultShippingAddress'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'defaultBillingAddress'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CreateAdministratorInput': { kind: 'INPUT_OBJECT'; name: 'CreateAdministratorInput'; isOneOf: false; inputFields: [{ name: 'firstName'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'lastName'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'emailAddress'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'password'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'roleIds'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CreateAssetInput': { kind: 'INPUT_OBJECT'; name: 'CreateAssetInput'; isOneOf: false; inputFields: [{ name: 'file'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Upload'; ofType: null; }; }; defaultValue: null }, { name: 'tags'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CreateAssetResult': { kind: 'UNION'; name: 'CreateAssetResult'; fields: {}; possibleTypes: 'Asset' | 'MimeTypeError'; };
    'CreateChannelInput': { kind: 'INPUT_OBJECT'; name: 'CreateChannelInput'; isOneOf: false; inputFields: [{ name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'token'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'defaultLanguageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; }; defaultValue: null }, { name: 'availableLanguageCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; }; }; defaultValue: null }, { name: 'pricesIncludeTax'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; }; defaultValue: null }, { name: 'currencyCode'; type: { kind: 'ENUM'; name: 'CurrencyCode'; ofType: null; }; defaultValue: null }, { name: 'defaultCurrencyCode'; type: { kind: 'ENUM'; name: 'CurrencyCode'; ofType: null; }; defaultValue: null }, { name: 'availableCurrencyCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'CurrencyCode'; ofType: null; }; }; }; defaultValue: null }, { name: 'trackInventory'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'outOfStockThreshold'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'defaultTaxZoneId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'defaultShippingZoneId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'sellerId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CreateChannelResult': { kind: 'UNION'; name: 'CreateChannelResult'; fields: {}; possibleTypes: 'Channel' | 'LanguageNotAvailableError'; };
    'CreateCollectionInput': { kind: 'INPUT_OBJECT'; name: 'CreateCollectionInput'; isOneOf: false; inputFields: [{ name: 'isPrivate'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'featuredAssetId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'assetIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; defaultValue: null }, { name: 'parentId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'inheritFilters'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'filters'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ConfigurableOperationInput'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'CreateCollectionTranslationInput'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CreateCollectionTranslationInput': { kind: 'INPUT_OBJECT'; name: 'CreateCollectionTranslationInput'; isOneOf: false; inputFields: [{ name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; }; defaultValue: null }, { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'slug'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CreateCountryInput': { kind: 'INPUT_OBJECT'; name: 'CreateCountryInput'; isOneOf: false; inputFields: [{ name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'CountryTranslationInput'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'enabled'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CreateCustomerGroupInput': { kind: 'INPUT_OBJECT'; name: 'CreateCustomerGroupInput'; isOneOf: false; inputFields: [{ name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'customerIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CreateCustomerInput': { kind: 'INPUT_OBJECT'; name: 'CreateCustomerInput'; isOneOf: false; inputFields: [{ name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'firstName'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'lastName'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'phoneNumber'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'emailAddress'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CreateCustomerResult': { kind: 'UNION'; name: 'CreateCustomerResult'; fields: {}; possibleTypes: 'Customer' | 'EmailAddressConflictError'; };
    'CreateFacetInput': { kind: 'INPUT_OBJECT'; name: 'CreateFacetInput'; isOneOf: false; inputFields: [{ name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'isPrivate'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; }; defaultValue: null }, { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'FacetTranslationInput'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'values'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'CreateFacetValueWithFacetInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CreateFacetValueInput': { kind: 'INPUT_OBJECT'; name: 'CreateFacetValueInput'; isOneOf: false; inputFields: [{ name: 'facetId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'FacetValueTranslationInput'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CreateFacetValueWithFacetInput': { kind: 'INPUT_OBJECT'; name: 'CreateFacetValueWithFacetInput'; isOneOf: false; inputFields: [{ name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'FacetValueTranslationInput'; ofType: null; }; }; }; }; defaultValue: null }]; };
    'CreateFulfillmentError': { kind: 'OBJECT'; name: 'CreateFulfillmentError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'fulfillmentHandlerError': { name: 'fulfillmentHandlerError'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'CreateGroupOptionInput': { kind: 'INPUT_OBJECT'; name: 'CreateGroupOptionInput'; isOneOf: false; inputFields: [{ name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ProductOptionGroupTranslationInput'; ofType: null; }; }; }; }; defaultValue: null }]; };
    'CreatePaymentMethodInput': { kind: 'INPUT_OBJECT'; name: 'CreatePaymentMethodInput'; isOneOf: false; inputFields: [{ name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'enabled'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; }; defaultValue: null }, { name: 'checker'; type: { kind: 'INPUT_OBJECT'; name: 'ConfigurableOperationInput'; ofType: null; }; defaultValue: null }, { name: 'handler'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ConfigurableOperationInput'; ofType: null; }; }; defaultValue: null }, { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'PaymentMethodTranslationInput'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CreateProductInput': { kind: 'INPUT_OBJECT'; name: 'CreateProductInput'; isOneOf: false; inputFields: [{ name: 'featuredAssetId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'enabled'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'assetIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; defaultValue: null }, { name: 'facetValueIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; defaultValue: null }, { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ProductTranslationInput'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CreateProductOptionGroupInput': { kind: 'INPUT_OBJECT'; name: 'CreateProductOptionGroupInput'; isOneOf: false; inputFields: [{ name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ProductOptionGroupTranslationInput'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'options'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'CreateGroupOptionInput'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CreateProductOptionInput': { kind: 'INPUT_OBJECT'; name: 'CreateProductOptionInput'; isOneOf: false; inputFields: [{ name: 'productOptionGroupId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ProductOptionGroupTranslationInput'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CreateProductVariantInput': { kind: 'INPUT_OBJECT'; name: 'CreateProductVariantInput'; isOneOf: false; inputFields: [{ name: 'productId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'enabled'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ProductVariantTranslationInput'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'facetValueIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; defaultValue: null }, { name: 'sku'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'price'; type: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; defaultValue: null }, { name: 'prices'; type: { kind: 'LIST'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'CreateProductVariantPriceInput'; ofType: null; }; }; defaultValue: null }, { name: 'taxCategoryId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'optionIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; defaultValue: null }, { name: 'featuredAssetId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'assetIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; defaultValue: null }, { name: 'stockOnHand'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'stockLevels'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'StockLevelInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'outOfStockThreshold'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'useGlobalOutOfStockThreshold'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'trackInventory'; type: { kind: 'ENUM'; name: 'GlobalFlag'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CreateProductVariantOptionInput': { kind: 'INPUT_OBJECT'; name: 'CreateProductVariantOptionInput'; isOneOf: false; inputFields: [{ name: 'optionGroupId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ProductOptionTranslationInput'; ofType: null; }; }; }; }; defaultValue: null }]; };
    'CreateProductVariantPriceInput': { kind: 'INPUT_OBJECT'; name: 'CreateProductVariantPriceInput'; isOneOf: false; inputFields: [{ name: 'currencyCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'CurrencyCode'; ofType: null; }; }; defaultValue: null }, { name: 'price'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CreatePromotionInput': { kind: 'INPUT_OBJECT'; name: 'CreatePromotionInput'; isOneOf: false; inputFields: [{ name: 'enabled'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; }; defaultValue: null }, { name: 'startsAt'; type: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; defaultValue: null }, { name: 'endsAt'; type: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; defaultValue: null }, { name: 'couponCode'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'perCustomerUsageLimit'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'usageLimit'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'conditions'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ConfigurableOperationInput'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'actions'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ConfigurableOperationInput'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'PromotionTranslationInput'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CreatePromotionResult': { kind: 'UNION'; name: 'CreatePromotionResult'; fields: {}; possibleTypes: 'MissingConditionsError' | 'Promotion'; };
    'CreateProvinceInput': { kind: 'INPUT_OBJECT'; name: 'CreateProvinceInput'; isOneOf: false; inputFields: [{ name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ProvinceTranslationInput'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'enabled'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CreateRoleInput': { kind: 'INPUT_OBJECT'; name: 'CreateRoleInput'; isOneOf: false; inputFields: [{ name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'permissions'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'channelIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; defaultValue: null }]; };
    'CreateSellerInput': { kind: 'INPUT_OBJECT'; name: 'CreateSellerInput'; isOneOf: false; inputFields: [{ name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CreateShippingMethodInput': { kind: 'INPUT_OBJECT'; name: 'CreateShippingMethodInput'; isOneOf: false; inputFields: [{ name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'fulfillmentHandler'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'checker'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ConfigurableOperationInput'; ofType: null; }; }; defaultValue: null }, { name: 'calculator'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ConfigurableOperationInput'; ofType: null; }; }; defaultValue: null }, { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ShippingMethodTranslationInput'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CreateStockLocationInput': { kind: 'INPUT_OBJECT'; name: 'CreateStockLocationInput'; isOneOf: false; inputFields: [{ name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CreateTagInput': { kind: 'INPUT_OBJECT'; name: 'CreateTagInput'; isOneOf: false; inputFields: [{ name: 'value'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }]; };
    'CreateTaxCategoryInput': { kind: 'INPUT_OBJECT'; name: 'CreateTaxCategoryInput'; isOneOf: false; inputFields: [{ name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'isDefault'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CreateTaxRateInput': { kind: 'INPUT_OBJECT'; name: 'CreateTaxRateInput'; isOneOf: false; inputFields: [{ name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'enabled'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; }; defaultValue: null }, { name: 'value'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; }; defaultValue: null }, { name: 'categoryId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'zoneId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'customerGroupId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CreateZoneInput': { kind: 'INPUT_OBJECT'; name: 'CreateZoneInput'; isOneOf: false; inputFields: [{ name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'memberIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'CurrencyCode': { name: 'CurrencyCode'; enumValues: 'AED' | 'AFN' | 'ALL' | 'AMD' | 'ANG' | 'AOA' | 'ARS' | 'AUD' | 'AWG' | 'AZN' | 'BAM' | 'BBD' | 'BDT' | 'BGN' | 'BHD' | 'BIF' | 'BMD' | 'BND' | 'BOB' | 'BRL' | 'BSD' | 'BTN' | 'BWP' | 'BYN' | 'BZD' | 'CAD' | 'CDF' | 'CHF' | 'CLP' | 'CNY' | 'COP' | 'CRC' | 'CUC' | 'CUP' | 'CVE' | 'CZK' | 'DJF' | 'DKK' | 'DOP' | 'DZD' | 'EGP' | 'ERN' | 'ETB' | 'EUR' | 'FJD' | 'FKP' | 'GBP' | 'GEL' | 'GHS' | 'GIP' | 'GMD' | 'GNF' | 'GTQ' | 'GYD' | 'HKD' | 'HNL' | 'HRK' | 'HTG' | 'HUF' | 'IDR' | 'ILS' | 'INR' | 'IQD' | 'IRR' | 'ISK' | 'JMD' | 'JOD' | 'JPY' | 'KES' | 'KGS' | 'KHR' | 'KMF' | 'KPW' | 'KRW' | 'KWD' | 'KYD' | 'KZT' | 'LAK' | 'LBP' | 'LKR' | 'LRD' | 'LSL' | 'LYD' | 'MAD' | 'MDL' | 'MGA' | 'MKD' | 'MMK' | 'MNT' | 'MOP' | 'MRU' | 'MUR' | 'MVR' | 'MWK' | 'MXN' | 'MYR' | 'MZN' | 'NAD' | 'NGN' | 'NIO' | 'NOK' | 'NPR' | 'NZD' | 'OMR' | 'PAB' | 'PEN' | 'PGK' | 'PHP' | 'PKR' | 'PLN' | 'PYG' | 'QAR' | 'RON' | 'RSD' | 'RUB' | 'RWF' | 'SAR' | 'SBD' | 'SCR' | 'SDG' | 'SEK' | 'SGD' | 'SHP' | 'SLL' | 'SOS' | 'SRD' | 'SSP' | 'STN' | 'SVC' | 'SYP' | 'SZL' | 'THB' | 'TJS' | 'TMT' | 'TND' | 'TOP' | 'TRY' | 'TTD' | 'TWD' | 'TZS' | 'UAH' | 'UGX' | 'USD' | 'UYU' | 'UZS' | 'VES' | 'VND' | 'VUV' | 'WST' | 'XAF' | 'XCD' | 'XOF' | 'XPF' | 'YER' | 'ZAR' | 'ZMW' | 'ZWL'; };
    'CurrentUser': { kind: 'OBJECT'; name: 'CurrentUser'; fields: { 'channels': { name: 'channels'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'CurrentUserChannel'; ofType: null; }; }; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'identifier': { name: 'identifier'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'CurrentUserChannel': { kind: 'OBJECT'; name: 'CurrentUserChannel'; fields: { 'code': { name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'permissions': { name: 'permissions'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; }; } }; 'token': { name: 'token'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'CustomField': { kind: 'INTERFACE'; name: 'CustomField'; fields: { 'description': { name: 'description'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'internal': { name: 'internal'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'list': { name: 'list'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'nullable': { name: 'nullable'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'readonly': { name: 'readonly'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiresPermission': { name: 'requiresPermission'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'ui': { name: 'ui'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; possibleTypes: 'BooleanCustomFieldConfig' | 'DateTimeCustomFieldConfig' | 'FloatCustomFieldConfig' | 'IntCustomFieldConfig' | 'LocaleStringCustomFieldConfig' | 'LocaleTextCustomFieldConfig' | 'RelationCustomFieldConfig' | 'StringCustomFieldConfig' | 'StructCustomFieldConfig' | 'TextCustomFieldConfig'; };
    'CustomFieldConfig': { kind: 'UNION'; name: 'CustomFieldConfig'; fields: {}; possibleTypes: 'BooleanCustomFieldConfig' | 'DateTimeCustomFieldConfig' | 'FloatCustomFieldConfig' | 'IntCustomFieldConfig' | 'LocaleStringCustomFieldConfig' | 'LocaleTextCustomFieldConfig' | 'RelationCustomFieldConfig' | 'StringCustomFieldConfig' | 'StructCustomFieldConfig' | 'TextCustomFieldConfig'; };
    'CustomFields': { kind: 'OBJECT'; name: 'CustomFields'; fields: { 'Address': { name: 'Address'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'Administrator': { name: 'Administrator'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'Asset': { name: 'Asset'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'Channel': { name: 'Channel'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'Collection': { name: 'Collection'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'Customer': { name: 'Customer'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'CustomerGroup': { name: 'CustomerGroup'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'Facet': { name: 'Facet'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'FacetValue': { name: 'FacetValue'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'Fulfillment': { name: 'Fulfillment'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'GlobalSettings': { name: 'GlobalSettings'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'HistoryEntry': { name: 'HistoryEntry'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'Order': { name: 'Order'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'OrderLine': { name: 'OrderLine'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'Payment': { name: 'Payment'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'PaymentMethod': { name: 'PaymentMethod'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'Product': { name: 'Product'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'ProductOption': { name: 'ProductOption'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'ProductOptionGroup': { name: 'ProductOptionGroup'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'ProductVariant': { name: 'ProductVariant'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'ProductVariantPrice': { name: 'ProductVariantPrice'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'Promotion': { name: 'Promotion'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'Refund': { name: 'Refund'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'Region': { name: 'Region'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'Seller': { name: 'Seller'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'Session': { name: 'Session'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'ShippingLine': { name: 'ShippingLine'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'ShippingMethod': { name: 'ShippingMethod'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'StockLevel': { name: 'StockLevel'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'StockLocation': { name: 'StockLocation'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'StockMovement': { name: 'StockMovement'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'TaxCategory': { name: 'TaxCategory'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'TaxRate': { name: 'TaxRate'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'User': { name: 'User'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'Zone': { name: 'Zone'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; }; };
    'Customer': { kind: 'OBJECT'; name: 'Customer'; fields: { 'addresses': { name: 'addresses'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Address'; ofType: null; }; }; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'emailAddress': { name: 'emailAddress'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'firstName': { name: 'firstName'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'groups': { name: 'groups'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'CustomerGroup'; ofType: null; }; }; }; } }; 'history': { name: 'history'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'HistoryEntryList'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'lastName': { name: 'lastName'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'orders': { name: 'orders'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'OrderList'; ofType: null; }; } }; 'phoneNumber': { name: 'phoneNumber'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'title': { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'user': { name: 'user'; type: { kind: 'OBJECT'; name: 'User'; ofType: null; } }; }; };
    'CustomerFilterParameter': { kind: 'INPUT_OBJECT'; name: 'CustomerFilterParameter'; isOneOf: false; inputFields: [{ name: 'postalCode'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'title'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'firstName'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'lastName'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'phoneNumber'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'emailAddress'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'CustomerFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'CustomerFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'CustomerGroup': { kind: 'OBJECT'; name: 'CustomerGroup'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'customers': { name: 'customers'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'CustomerList'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'CustomerGroupFilterParameter': { kind: 'INPUT_OBJECT'; name: 'CustomerGroupFilterParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'CustomerGroupFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'CustomerGroupFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'CustomerGroupList': { kind: 'OBJECT'; name: 'CustomerGroupList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'CustomerGroup'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'CustomerGroupListOptions': { kind: 'INPUT_OBJECT'; name: 'CustomerGroupListOptions'; isOneOf: false; inputFields: [{ name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'CustomerGroupSortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'CustomerGroupFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'CustomerGroupSortParameter': { kind: 'INPUT_OBJECT'; name: 'CustomerGroupSortParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'CustomerList': { kind: 'OBJECT'; name: 'CustomerList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Customer'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'CustomerListOptions': { kind: 'INPUT_OBJECT'; name: 'CustomerListOptions'; isOneOf: false; inputFields: [{ name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'CustomerSortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'CustomerFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'CustomerSortParameter': { kind: 'INPUT_OBJECT'; name: 'CustomerSortParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'title'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'firstName'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'lastName'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'phoneNumber'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'emailAddress'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'DateListOperators': { kind: 'INPUT_OBJECT'; name: 'DateListOperators'; isOneOf: false; inputFields: [{ name: 'inList'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; }; defaultValue: null }]; };
    'DateOperators': { kind: 'INPUT_OBJECT'; name: 'DateOperators'; isOneOf: false; inputFields: [{ name: 'eq'; type: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; defaultValue: null }, { name: 'before'; type: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; defaultValue: null }, { name: 'after'; type: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; defaultValue: null }, { name: 'between'; type: { kind: 'INPUT_OBJECT'; name: 'DateRange'; ofType: null; }; defaultValue: null }, { name: 'isNull'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }]; };
    'DateRange': { kind: 'INPUT_OBJECT'; name: 'DateRange'; isOneOf: false; inputFields: [{ name: 'start'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; }; defaultValue: null }, { name: 'end'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; }; defaultValue: null }]; };
    'DateTime': unknown;
    'DateTimeCustomFieldConfig': { kind: 'OBJECT'; name: 'DateTimeCustomFieldConfig'; fields: { 'description': { name: 'description'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'internal': { name: 'internal'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'list': { name: 'list'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'max': { name: 'max'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'min': { name: 'min'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'nullable': { name: 'nullable'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'readonly': { name: 'readonly'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiresPermission': { name: 'requiresPermission'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; } }; 'step': { name: 'step'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'ui': { name: 'ui'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; };
    'DateTimeStructFieldConfig': { kind: 'OBJECT'; name: 'DateTimeStructFieldConfig'; fields: { 'description': { name: 'description'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'label': { name: 'label'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'list': { name: 'list'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'max': { name: 'max'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'min': { name: 'min'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'step': { name: 'step'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'ui': { name: 'ui'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; };
    'DeleteAssetInput': { kind: 'INPUT_OBJECT'; name: 'DeleteAssetInput'; isOneOf: false; inputFields: [{ name: 'assetId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'force'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'deleteFromAllChannels'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }]; };
    'DeleteAssetsInput': { kind: 'INPUT_OBJECT'; name: 'DeleteAssetsInput'; isOneOf: false; inputFields: [{ name: 'assetIds'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'force'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'deleteFromAllChannels'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }]; };
    'DeleteStockLocationInput': { kind: 'INPUT_OBJECT'; name: 'DeleteStockLocationInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'transferToLocationId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }]; };
    'DeletionResponse': { kind: 'OBJECT'; name: 'DeletionResponse'; fields: { 'message': { name: 'message'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'result': { name: 'result'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'DeletionResult'; ofType: null; }; } }; }; };
    'DeletionResult': { name: 'DeletionResult'; enumValues: 'DELETED' | 'NOT_DELETED'; };
    'Discount': { kind: 'OBJECT'; name: 'Discount'; fields: { 'adjustmentSource': { name: 'adjustmentSource'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'amount': { name: 'amount'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'amountWithTax': { name: 'amountWithTax'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'description': { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'AdjustmentType'; ofType: null; }; } }; }; };
    'DuplicateEntityError': { kind: 'OBJECT'; name: 'DuplicateEntityError'; fields: { 'duplicationError': { name: 'duplicationError'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'DuplicateEntityInput': { kind: 'INPUT_OBJECT'; name: 'DuplicateEntityInput'; isOneOf: false; inputFields: [{ name: 'entityName'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'entityId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'duplicatorInput'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ConfigurableOperationInput'; ofType: null; }; }; defaultValue: null }]; };
    'DuplicateEntityResult': { kind: 'UNION'; name: 'DuplicateEntityResult'; fields: {}; possibleTypes: 'DuplicateEntityError' | 'DuplicateEntitySuccess'; };
    'DuplicateEntitySuccess': { kind: 'OBJECT'; name: 'DuplicateEntitySuccess'; fields: { 'newEntityId': { name: 'newEntityId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; }; };
    'EmailAddressConflictError': { kind: 'OBJECT'; name: 'EmailAddressConflictError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'EmptyOrderLineSelectionError': { kind: 'OBJECT'; name: 'EmptyOrderLineSelectionError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'EntityCustomFields': { kind: 'OBJECT'; name: 'EntityCustomFields'; fields: { 'customFields': { name: 'customFields'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CustomFieldConfig'; ofType: null; }; }; }; } }; 'entityName': { name: 'entityName'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'EntityDuplicatorDefinition': { kind: 'OBJECT'; name: 'EntityDuplicatorDefinition'; fields: { 'args': { name: 'args'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ConfigArgDefinition'; ofType: null; }; }; }; } }; 'code': { name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'description': { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'forEntities': { name: 'forEntities'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; }; } }; 'requiresPermission': { name: 'requiresPermission'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; }; } }; }; };
    'ErrorCode': { name: 'ErrorCode'; enumValues: 'UNKNOWN_ERROR' | 'MIME_TYPE_ERROR' | 'LANGUAGE_NOT_AVAILABLE_ERROR' | 'DUPLICATE_ENTITY_ERROR' | 'FACET_IN_USE_ERROR' | 'CHANNEL_DEFAULT_LANGUAGE_ERROR' | 'SETTLE_PAYMENT_ERROR' | 'CANCEL_PAYMENT_ERROR' | 'EMPTY_ORDER_LINE_SELECTION_ERROR' | 'ITEMS_ALREADY_FULFILLED_ERROR' | 'INVALID_FULFILLMENT_HANDLER_ERROR' | 'CREATE_FULFILLMENT_ERROR' | 'INSUFFICIENT_STOCK_ON_HAND_ERROR' | 'MULTIPLE_ORDER_ERROR' | 'CANCEL_ACTIVE_ORDER_ERROR' | 'PAYMENT_ORDER_MISMATCH_ERROR' | 'REFUND_ORDER_STATE_ERROR' | 'NOTHING_TO_REFUND_ERROR' | 'ALREADY_REFUNDED_ERROR' | 'QUANTITY_TOO_GREAT_ERROR' | 'REFUND_AMOUNT_ERROR' | 'REFUND_STATE_TRANSITION_ERROR' | 'PAYMENT_STATE_TRANSITION_ERROR' | 'FULFILLMENT_STATE_TRANSITION_ERROR' | 'ORDER_MODIFICATION_STATE_ERROR' | 'NO_CHANGES_SPECIFIED_ERROR' | 'PAYMENT_METHOD_MISSING_ERROR' | 'REFUND_PAYMENT_ID_MISSING_ERROR' | 'MANUAL_PAYMENT_STATE_ERROR' | 'PRODUCT_OPTION_IN_USE_ERROR' | 'MISSING_CONDITIONS_ERROR' | 'NATIVE_AUTH_STRATEGY_ERROR' | 'INVALID_CREDENTIALS_ERROR' | 'ORDER_STATE_TRANSITION_ERROR' | 'EMAIL_ADDRESS_CONFLICT_ERROR' | 'GUEST_CHECKOUT_ERROR' | 'ORDER_LIMIT_ERROR' | 'NEGATIVE_QUANTITY_ERROR' | 'INSUFFICIENT_STOCK_ERROR' | 'COUPON_CODE_INVALID_ERROR' | 'COUPON_CODE_EXPIRED_ERROR' | 'COUPON_CODE_LIMIT_ERROR' | 'ORDER_MODIFICATION_ERROR' | 'INELIGIBLE_SHIPPING_METHOD_ERROR' | 'NO_ACTIVE_ORDER_ERROR' | 'ORDER_INTERCEPTOR_ERROR'; };
    'ErrorResult': { kind: 'INTERFACE'; name: 'ErrorResult'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; possibleTypes: 'AlreadyRefundedError' | 'CancelActiveOrderError' | 'CancelPaymentError' | 'ChannelDefaultLanguageError' | 'CouponCodeExpiredError' | 'CouponCodeInvalidError' | 'CouponCodeLimitError' | 'CreateFulfillmentError' | 'DuplicateEntityError' | 'EmailAddressConflictError' | 'EmptyOrderLineSelectionError' | 'FacetInUseError' | 'FulfillmentStateTransitionError' | 'GuestCheckoutError' | 'IneligibleShippingMethodError' | 'InsufficientStockError' | 'InsufficientStockOnHandError' | 'InvalidCredentialsError' | 'InvalidFulfillmentHandlerError' | 'ItemsAlreadyFulfilledError' | 'LanguageNotAvailableError' | 'ManualPaymentStateError' | 'MimeTypeError' | 'MissingConditionsError' | 'MultipleOrderError' | 'NativeAuthStrategyError' | 'NegativeQuantityError' | 'NoActiveOrderError' | 'NoChangesSpecifiedError' | 'NothingToRefundError' | 'OrderInterceptorError' | 'OrderLimitError' | 'OrderModificationError' | 'OrderModificationStateError' | 'OrderStateTransitionError' | 'PaymentMethodMissingError' | 'PaymentOrderMismatchError' | 'PaymentStateTransitionError' | 'ProductOptionInUseError' | 'QuantityTooGreatError' | 'RefundAmountError' | 'RefundOrderStateError' | 'RefundPaymentIdMissingError' | 'RefundStateTransitionError' | 'SettlePaymentError'; };
    'Facet': { kind: 'OBJECT'; name: 'Facet'; fields: { 'code': { name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'isPrivate': { name: 'isPrivate'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'languageCode': { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'translations': { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'FacetTranslation'; ofType: null; }; }; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'valueList': { name: 'valueList'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'FacetValueList'; ofType: null; }; } }; 'values': { name: 'values'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'FacetValue'; ofType: null; }; }; }; } }; }; };
    'FacetFilterParameter': { kind: 'INPUT_OBJECT'; name: 'FacetFilterParameter'; isOneOf: false; inputFields: [{ name: 'isPrivate'; type: { kind: 'INPUT_OBJECT'; name: 'BooleanOperators'; ofType: null; }; defaultValue: null }, { name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'languageCode'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'code'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'FacetFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'FacetFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'FacetInUseError': { kind: 'OBJECT'; name: 'FacetInUseError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'facetCode': { name: 'facetCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'productCount': { name: 'productCount'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'variantCount': { name: 'variantCount'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'FacetList': { kind: 'OBJECT'; name: 'FacetList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Facet'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'FacetListOptions': { kind: 'INPUT_OBJECT'; name: 'FacetListOptions'; isOneOf: false; inputFields: [{ name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'FacetSortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'FacetFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'FacetSortParameter': { kind: 'INPUT_OBJECT'; name: 'FacetSortParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'code'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'FacetTranslation': { kind: 'OBJECT'; name: 'FacetTranslation'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'languageCode': { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'FacetTranslationInput': { kind: 'INPUT_OBJECT'; name: 'FacetTranslationInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'FacetValue': { kind: 'OBJECT'; name: 'FacetValue'; fields: { 'code': { name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'facet': { name: 'facet'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Facet'; ofType: null; }; } }; 'facetId': { name: 'facetId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'languageCode': { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'translations': { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'FacetValueTranslation'; ofType: null; }; }; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'FacetValueFilterInput': { kind: 'INPUT_OBJECT'; name: 'FacetValueFilterInput'; isOneOf: false; inputFields: [{ name: 'and'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; defaultValue: null }]; };
    'FacetValueFilterParameter': { kind: 'INPUT_OBJECT'; name: 'FacetValueFilterParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'languageCode'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'facetId'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'code'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'FacetValueFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'FacetValueFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'FacetValueList': { kind: 'OBJECT'; name: 'FacetValueList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'FacetValue'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'FacetValueListOptions': { kind: 'INPUT_OBJECT'; name: 'FacetValueListOptions'; isOneOf: false; inputFields: [{ name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'FacetValueSortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'FacetValueFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'FacetValueResult': { kind: 'OBJECT'; name: 'FacetValueResult'; fields: { 'count': { name: 'count'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'facetValue': { name: 'facetValue'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'FacetValue'; ofType: null; }; } }; }; };
    'FacetValueSortParameter': { kind: 'INPUT_OBJECT'; name: 'FacetValueSortParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'facetId'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'code'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'FacetValueTranslation': { kind: 'OBJECT'; name: 'FacetValueTranslation'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'languageCode': { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'FacetValueTranslationInput': { kind: 'INPUT_OBJECT'; name: 'FacetValueTranslationInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'Float': unknown;
    'FloatCustomFieldConfig': { kind: 'OBJECT'; name: 'FloatCustomFieldConfig'; fields: { 'description': { name: 'description'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'internal': { name: 'internal'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'list': { name: 'list'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'max': { name: 'max'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; } }; 'min': { name: 'min'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'nullable': { name: 'nullable'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'readonly': { name: 'readonly'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiresPermission': { name: 'requiresPermission'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; } }; 'step': { name: 'step'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'ui': { name: 'ui'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; };
    'FloatStructFieldConfig': { kind: 'OBJECT'; name: 'FloatStructFieldConfig'; fields: { 'description': { name: 'description'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'label': { name: 'label'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'list': { name: 'list'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'max': { name: 'max'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; } }; 'min': { name: 'min'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'step': { name: 'step'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'ui': { name: 'ui'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; };
    'FulfillOrderInput': { kind: 'INPUT_OBJECT'; name: 'FulfillOrderInput'; isOneOf: false; inputFields: [{ name: 'lines'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'OrderLineInput'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'handler'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ConfigurableOperationInput'; ofType: null; }; }; defaultValue: null }]; };
    'Fulfillment': { kind: 'OBJECT'; name: 'Fulfillment'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'lines': { name: 'lines'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'FulfillmentLine'; ofType: null; }; }; }; } }; 'method': { name: 'method'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'nextStates': { name: 'nextStates'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; }; } }; 'state': { name: 'state'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'summary': { name: 'summary'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'FulfillmentLine'; ofType: null; }; }; }; } }; 'trackingCode': { name: 'trackingCode'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'FulfillmentLine': { kind: 'OBJECT'; name: 'FulfillmentLine'; fields: { 'fulfillment': { name: 'fulfillment'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Fulfillment'; ofType: null; }; } }; 'fulfillmentId': { name: 'fulfillmentId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'orderLine': { name: 'orderLine'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'OrderLine'; ofType: null; }; } }; 'orderLineId': { name: 'orderLineId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'quantity': { name: 'quantity'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'FulfillmentStateTransitionError': { kind: 'OBJECT'; name: 'FulfillmentStateTransitionError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'fromState': { name: 'fromState'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'toState': { name: 'toState'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'transitionError': { name: 'transitionError'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'GlobalFlag': { name: 'GlobalFlag'; enumValues: 'TRUE' | 'FALSE' | 'INHERIT'; };
    'GlobalSettings': { kind: 'OBJECT'; name: 'GlobalSettings'; fields: { 'availableLanguages': { name: 'availableLanguages'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; }; }; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'outOfStockThreshold': { name: 'outOfStockThreshold'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'serverConfig': { name: 'serverConfig'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ServerConfig'; ofType: null; }; } }; 'trackInventory': { name: 'trackInventory'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'GuestCheckoutError': { kind: 'OBJECT'; name: 'GuestCheckoutError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'errorDetail': { name: 'errorDetail'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'HistoryEntry': { kind: 'OBJECT'; name: 'HistoryEntry'; fields: { 'administrator': { name: 'administrator'; type: { kind: 'OBJECT'; name: 'Administrator'; ofType: null; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'data': { name: 'data'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'isPublic': { name: 'isPublic'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'HistoryEntryType'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'HistoryEntryFilterParameter': { kind: 'INPUT_OBJECT'; name: 'HistoryEntryFilterParameter'; isOneOf: false; inputFields: [{ name: 'isPublic'; type: { kind: 'INPUT_OBJECT'; name: 'BooleanOperators'; ofType: null; }; defaultValue: null }, { name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'type'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'HistoryEntryFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'HistoryEntryFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'HistoryEntryList': { kind: 'OBJECT'; name: 'HistoryEntryList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'HistoryEntry'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'HistoryEntryListOptions': { kind: 'INPUT_OBJECT'; name: 'HistoryEntryListOptions'; isOneOf: false; inputFields: [{ name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'HistoryEntrySortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'HistoryEntryFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'HistoryEntrySortParameter': { kind: 'INPUT_OBJECT'; name: 'HistoryEntrySortParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'HistoryEntryType': { name: 'HistoryEntryType'; enumValues: 'CUSTOMER_REGISTERED' | 'CUSTOMER_VERIFIED' | 'CUSTOMER_DETAIL_UPDATED' | 'CUSTOMER_ADDED_TO_GROUP' | 'CUSTOMER_REMOVED_FROM_GROUP' | 'CUSTOMER_ADDRESS_CREATED' | 'CUSTOMER_ADDRESS_UPDATED' | 'CUSTOMER_ADDRESS_DELETED' | 'CUSTOMER_PASSWORD_UPDATED' | 'CUSTOMER_PASSWORD_RESET_REQUESTED' | 'CUSTOMER_PASSWORD_RESET_VERIFIED' | 'CUSTOMER_EMAIL_UPDATE_REQUESTED' | 'CUSTOMER_EMAIL_UPDATE_VERIFIED' | 'CUSTOMER_NOTE' | 'ORDER_STATE_TRANSITION' | 'ORDER_PAYMENT_TRANSITION' | 'ORDER_FULFILLMENT' | 'ORDER_CANCELLATION' | 'ORDER_REFUND_TRANSITION' | 'ORDER_FULFILLMENT_TRANSITION' | 'ORDER_NOTE' | 'ORDER_COUPON_APPLIED' | 'ORDER_COUPON_REMOVED' | 'ORDER_MODIFIED' | 'ORDER_CUSTOMER_UPDATED'; };
    'ID': unknown;
    'IDListOperators': { kind: 'INPUT_OBJECT'; name: 'IDListOperators'; isOneOf: false; inputFields: [{ name: 'inList'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }]; };
    'IDOperators': { kind: 'INPUT_OBJECT'; name: 'IDOperators'; isOneOf: false; inputFields: [{ name: 'eq'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'notEq'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'in'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; }; defaultValue: null }, { name: 'notIn'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; }; defaultValue: null }, { name: 'isNull'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }]; };
    'ImportInfo': { kind: 'OBJECT'; name: 'ImportInfo'; fields: { 'errors': { name: 'errors'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; } }; 'imported': { name: 'imported'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'processed': { name: 'processed'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'IneligibleShippingMethodError': { kind: 'OBJECT'; name: 'IneligibleShippingMethodError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'InsufficientStockError': { kind: 'OBJECT'; name: 'InsufficientStockError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'order': { name: 'order'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Order'; ofType: null; }; } }; 'quantityAvailable': { name: 'quantityAvailable'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'InsufficientStockOnHandError': { kind: 'OBJECT'; name: 'InsufficientStockOnHandError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'productVariantId': { name: 'productVariantId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'productVariantName': { name: 'productVariantName'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'stockOnHand': { name: 'stockOnHand'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'Int': unknown;
    'IntCustomFieldConfig': { kind: 'OBJECT'; name: 'IntCustomFieldConfig'; fields: { 'description': { name: 'description'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'internal': { name: 'internal'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'list': { name: 'list'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'max': { name: 'max'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'min': { name: 'min'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'nullable': { name: 'nullable'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'readonly': { name: 'readonly'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiresPermission': { name: 'requiresPermission'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; } }; 'step': { name: 'step'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'ui': { name: 'ui'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; };
    'IntStructFieldConfig': { kind: 'OBJECT'; name: 'IntStructFieldConfig'; fields: { 'description': { name: 'description'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'label': { name: 'label'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'list': { name: 'list'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'max': { name: 'max'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'min': { name: 'min'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'step': { name: 'step'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'ui': { name: 'ui'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; };
    'InvalidCredentialsError': { kind: 'OBJECT'; name: 'InvalidCredentialsError'; fields: { 'authenticationError': { name: 'authenticationError'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'InvalidFulfillmentHandlerError': { kind: 'OBJECT'; name: 'InvalidFulfillmentHandlerError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'ItemsAlreadyFulfilledError': { kind: 'OBJECT'; name: 'ItemsAlreadyFulfilledError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'JSON': unknown;
    'Job': { kind: 'OBJECT'; name: 'Job'; fields: { 'attempts': { name: 'attempts'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'data': { name: 'data'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'duration': { name: 'duration'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'error': { name: 'error'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'isSettled': { name: 'isSettled'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'progress': { name: 'progress'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; } }; 'queueName': { name: 'queueName'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'result': { name: 'result'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'retries': { name: 'retries'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'settledAt': { name: 'settledAt'; type: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; } }; 'startedAt': { name: 'startedAt'; type: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; } }; 'state': { name: 'state'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'JobState'; ofType: null; }; } }; }; };
    'JobBufferSize': { kind: 'OBJECT'; name: 'JobBufferSize'; fields: { 'bufferId': { name: 'bufferId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'size': { name: 'size'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'JobFilterParameter': { kind: 'INPUT_OBJECT'; name: 'JobFilterParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'startedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'settledAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'queueName'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'state'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'progress'; type: { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; ofType: null; }; defaultValue: null }, { name: 'isSettled'; type: { kind: 'INPUT_OBJECT'; name: 'BooleanOperators'; ofType: null; }; defaultValue: null }, { name: 'duration'; type: { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; ofType: null; }; defaultValue: null }, { name: 'retries'; type: { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; ofType: null; }; defaultValue: null }, { name: 'attempts'; type: { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'JobFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'JobFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'JobList': { kind: 'OBJECT'; name: 'JobList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Job'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'JobListOptions': { kind: 'INPUT_OBJECT'; name: 'JobListOptions'; isOneOf: false; inputFields: [{ name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'JobSortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'JobFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'JobQueue': { kind: 'OBJECT'; name: 'JobQueue'; fields: { 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'running': { name: 'running'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; }; };
    'JobSortParameter': { kind: 'INPUT_OBJECT'; name: 'JobSortParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'startedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'settledAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'queueName'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'progress'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'duration'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'retries'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'attempts'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'JobState': { name: 'JobState'; enumValues: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'RETRYING' | 'FAILED' | 'CANCELLED'; };
    'LanguageCode': { name: 'LanguageCode'; enumValues: 'af' | 'ak' | 'sq' | 'am' | 'ar' | 'hy' | 'as' | 'az' | 'bm' | 'bn' | 'eu' | 'be' | 'bs' | 'br' | 'bg' | 'my' | 'ca' | 'ce' | 'zh' | 'zh_Hans' | 'zh_Hant' | 'cu' | 'kw' | 'co' | 'hr' | 'cs' | 'da' | 'nl' | 'nl_BE' | 'dz' | 'en' | 'en_AU' | 'en_CA' | 'en_GB' | 'en_US' | 'eo' | 'et' | 'ee' | 'fo' | 'fi' | 'fr' | 'fr_CA' | 'fr_CH' | 'ff' | 'gl' | 'lg' | 'ka' | 'de' | 'de_AT' | 'de_CH' | 'el' | 'gu' | 'ht' | 'ha' | 'he' | 'hi' | 'hu' | 'is' | 'ig' | 'id' | 'ia' | 'ga' | 'it' | 'ja' | 'jv' | 'kl' | 'kn' | 'ks' | 'kk' | 'km' | 'ki' | 'rw' | 'ko' | 'ku' | 'ky' | 'lo' | 'la' | 'lv' | 'ln' | 'lt' | 'lu' | 'lb' | 'mk' | 'mg' | 'ms' | 'ml' | 'mt' | 'gv' | 'mi' | 'mr' | 'mn' | 'ne' | 'nd' | 'se' | 'nb' | 'nn' | 'ny' | 'or' | 'om' | 'os' | 'ps' | 'fa' | 'fa_AF' | 'pl' | 'pt' | 'pt_BR' | 'pt_PT' | 'pa' | 'qu' | 'ro' | 'ro_MD' | 'rm' | 'rn' | 'ru' | 'sm' | 'sg' | 'sa' | 'gd' | 'sr' | 'sn' | 'ii' | 'sd' | 'si' | 'sk' | 'sl' | 'so' | 'st' | 'es' | 'es_ES' | 'es_MX' | 'su' | 'sw' | 'sw_CD' | 'sv' | 'tg' | 'ta' | 'tt' | 'te' | 'th' | 'bo' | 'ti' | 'to' | 'tr' | 'tk' | 'uk' | 'ur' | 'ug' | 'uz' | 'vi' | 'vo' | 'cy' | 'fy' | 'wo' | 'xh' | 'yi' | 'yo' | 'zu'; };
    'LanguageNotAvailableError': { kind: 'OBJECT'; name: 'LanguageNotAvailableError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'languageCode': { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'LocaleStringCustomFieldConfig': { kind: 'OBJECT'; name: 'LocaleStringCustomFieldConfig'; fields: { 'description': { name: 'description'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'internal': { name: 'internal'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'length': { name: 'length'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'list': { name: 'list'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'nullable': { name: 'nullable'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'pattern': { name: 'pattern'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'readonly': { name: 'readonly'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiresPermission': { name: 'requiresPermission'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'ui': { name: 'ui'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; };
    'LocaleTextCustomFieldConfig': { kind: 'OBJECT'; name: 'LocaleTextCustomFieldConfig'; fields: { 'description': { name: 'description'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'internal': { name: 'internal'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'list': { name: 'list'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'nullable': { name: 'nullable'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'readonly': { name: 'readonly'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiresPermission': { name: 'requiresPermission'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'ui': { name: 'ui'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; };
    'LocalizedString': { kind: 'OBJECT'; name: 'LocalizedString'; fields: { 'languageCode': { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; } }; 'value': { name: 'value'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'LogicalOperator': { name: 'LogicalOperator'; enumValues: 'AND' | 'OR'; };
    'ManualPaymentInput': { kind: 'INPUT_OBJECT'; name: 'ManualPaymentInput'; isOneOf: false; inputFields: [{ name: 'orderId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'method'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'transactionId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'metadata'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'ManualPaymentStateError': { kind: 'OBJECT'; name: 'ManualPaymentStateError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'MetricInterval': { name: 'MetricInterval'; enumValues: 'Daily'; };
    'MetricSummary': { kind: 'OBJECT'; name: 'MetricSummary'; fields: { 'entries': { name: 'entries'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'MetricSummaryEntry'; ofType: null; }; }; }; } }; 'interval': { name: 'interval'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'MetricInterval'; ofType: null; }; } }; 'title': { name: 'title'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'MetricType'; ofType: null; }; } }; }; };
    'MetricSummaryEntry': { kind: 'OBJECT'; name: 'MetricSummaryEntry'; fields: { 'label': { name: 'label'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'value': { name: 'value'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; } }; }; };
    'MetricSummaryInput': { kind: 'INPUT_OBJECT'; name: 'MetricSummaryInput'; isOneOf: false; inputFields: [{ name: 'interval'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'MetricInterval'; ofType: null; }; }; defaultValue: null }, { name: 'types'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'MetricType'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'refresh'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }]; };
    'MetricType': { name: 'MetricType'; enumValues: 'OrderCount' | 'OrderTotal' | 'AverageOrderValue'; };
    'MimeTypeError': { kind: 'OBJECT'; name: 'MimeTypeError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'fileName': { name: 'fileName'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'mimeType': { name: 'mimeType'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'MissingConditionsError': { kind: 'OBJECT'; name: 'MissingConditionsError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'ModifyOrderInput': { kind: 'INPUT_OBJECT'; name: 'ModifyOrderInput'; isOneOf: false; inputFields: [{ name: 'dryRun'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; }; defaultValue: null }, { name: 'orderId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'addItems'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'AddItemInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'adjustOrderLines'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'OrderLineInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'surcharges'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'SurchargeInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'updateShippingAddress'; type: { kind: 'INPUT_OBJECT'; name: 'UpdateOrderAddressInput'; ofType: null; }; defaultValue: null }, { name: 'updateBillingAddress'; type: { kind: 'INPUT_OBJECT'; name: 'UpdateOrderAddressInput'; ofType: null; }; defaultValue: null }, { name: 'note'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'refund'; type: { kind: 'INPUT_OBJECT'; name: 'AdministratorRefundInput'; ofType: null; }; defaultValue: null }, { name: 'refunds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'AdministratorRefundInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'options'; type: { kind: 'INPUT_OBJECT'; name: 'ModifyOrderOptions'; ofType: null; }; defaultValue: null }, { name: 'couponCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; }; defaultValue: null }, { name: 'shippingMethodIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; defaultValue: null }]; };
    'ModifyOrderOptions': { kind: 'INPUT_OBJECT'; name: 'ModifyOrderOptions'; isOneOf: false; inputFields: [{ name: 'freezePromotions'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'recalculateShipping'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }]; };
    'ModifyOrderResult': { kind: 'UNION'; name: 'ModifyOrderResult'; fields: {}; possibleTypes: 'CouponCodeExpiredError' | 'CouponCodeInvalidError' | 'CouponCodeLimitError' | 'IneligibleShippingMethodError' | 'InsufficientStockError' | 'NegativeQuantityError' | 'NoChangesSpecifiedError' | 'Order' | 'OrderLimitError' | 'OrderModificationStateError' | 'PaymentMethodMissingError' | 'RefundPaymentIdMissingError'; };
    'Money': unknown;
    'MoveCollectionInput': { kind: 'INPUT_OBJECT'; name: 'MoveCollectionInput'; isOneOf: false; inputFields: [{ name: 'collectionId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'parentId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'index'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; }; defaultValue: null }]; };
    'MultipleOrderError': { kind: 'OBJECT'; name: 'MultipleOrderError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'Mutation': { kind: 'OBJECT'; name: 'Mutation'; fields: { 'addCustomersToGroup': { name: 'addCustomersToGroup'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'CustomerGroup'; ofType: null; }; } }; 'addFulfillmentToOrder': { name: 'addFulfillmentToOrder'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'AddFulfillmentToOrderResult'; ofType: null; }; } }; 'addItemToDraftOrder': { name: 'addItemToDraftOrder'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'UpdateOrderItemsResult'; ofType: null; }; } }; 'addManualPaymentToOrder': { name: 'addManualPaymentToOrder'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'AddManualPaymentToOrderResult'; ofType: null; }; } }; 'addMembersToZone': { name: 'addMembersToZone'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Zone'; ofType: null; }; } }; 'addNoteToCustomer': { name: 'addNoteToCustomer'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Customer'; ofType: null; }; } }; 'addNoteToOrder': { name: 'addNoteToOrder'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Order'; ofType: null; }; } }; 'addOptionGroupToProduct': { name: 'addOptionGroupToProduct'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Product'; ofType: null; }; } }; 'adjustDraftOrderLine': { name: 'adjustDraftOrderLine'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'UpdateOrderItemsResult'; ofType: null; }; } }; 'applyCouponCodeToDraftOrder': { name: 'applyCouponCodeToDraftOrder'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'ApplyCouponCodeResult'; ofType: null; }; } }; 'assignAssetsToChannel': { name: 'assignAssetsToChannel'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Asset'; ofType: null; }; }; }; } }; 'assignCollectionsToChannel': { name: 'assignCollectionsToChannel'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Collection'; ofType: null; }; }; }; } }; 'assignFacetsToChannel': { name: 'assignFacetsToChannel'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Facet'; ofType: null; }; }; }; } }; 'assignPaymentMethodsToChannel': { name: 'assignPaymentMethodsToChannel'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'PaymentMethod'; ofType: null; }; }; }; } }; 'assignProductVariantsToChannel': { name: 'assignProductVariantsToChannel'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductVariant'; ofType: null; }; }; }; } }; 'assignProductsToChannel': { name: 'assignProductsToChannel'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Product'; ofType: null; }; }; }; } }; 'assignPromotionsToChannel': { name: 'assignPromotionsToChannel'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Promotion'; ofType: null; }; }; }; } }; 'assignRoleToAdministrator': { name: 'assignRoleToAdministrator'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Administrator'; ofType: null; }; } }; 'assignShippingMethodsToChannel': { name: 'assignShippingMethodsToChannel'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ShippingMethod'; ofType: null; }; }; }; } }; 'assignStockLocationsToChannel': { name: 'assignStockLocationsToChannel'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'StockLocation'; ofType: null; }; }; }; } }; 'authenticate': { name: 'authenticate'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'AuthenticationResult'; ofType: null; }; } }; 'cancelJob': { name: 'cancelJob'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Job'; ofType: null; }; } }; 'cancelOrder': { name: 'cancelOrder'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CancelOrderResult'; ofType: null; }; } }; 'cancelPayment': { name: 'cancelPayment'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CancelPaymentResult'; ofType: null; }; } }; 'createAdministrator': { name: 'createAdministrator'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Administrator'; ofType: null; }; } }; 'createAssets': { name: 'createAssets'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CreateAssetResult'; ofType: null; }; }; }; } }; 'createChannel': { name: 'createChannel'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CreateChannelResult'; ofType: null; }; } }; 'createCollection': { name: 'createCollection'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Collection'; ofType: null; }; } }; 'createCountry': { name: 'createCountry'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Country'; ofType: null; }; } }; 'createCustomer': { name: 'createCustomer'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CreateCustomerResult'; ofType: null; }; } }; 'createCustomerAddress': { name: 'createCustomerAddress'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Address'; ofType: null; }; } }; 'createCustomerGroup': { name: 'createCustomerGroup'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'CustomerGroup'; ofType: null; }; } }; 'createDraftOrder': { name: 'createDraftOrder'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Order'; ofType: null; }; } }; 'createFacet': { name: 'createFacet'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Facet'; ofType: null; }; } }; 'createFacetValues': { name: 'createFacetValues'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'FacetValue'; ofType: null; }; }; }; } }; 'createPaymentMethod': { name: 'createPaymentMethod'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'PaymentMethod'; ofType: null; }; } }; 'createProduct': { name: 'createProduct'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Product'; ofType: null; }; } }; 'createProductOption': { name: 'createProductOption'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductOption'; ofType: null; }; } }; 'createProductOptionGroup': { name: 'createProductOptionGroup'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductOptionGroup'; ofType: null; }; } }; 'createProductVariants': { name: 'createProductVariants'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductVariant'; ofType: null; }; }; } }; 'createPromotion': { name: 'createPromotion'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'CreatePromotionResult'; ofType: null; }; } }; 'createProvince': { name: 'createProvince'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Province'; ofType: null; }; } }; 'createRole': { name: 'createRole'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Role'; ofType: null; }; } }; 'createSeller': { name: 'createSeller'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Seller'; ofType: null; }; } }; 'createShippingMethod': { name: 'createShippingMethod'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ShippingMethod'; ofType: null; }; } }; 'createStockLocation': { name: 'createStockLocation'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'StockLocation'; ofType: null; }; } }; 'createTag': { name: 'createTag'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Tag'; ofType: null; }; } }; 'createTaxCategory': { name: 'createTaxCategory'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'TaxCategory'; ofType: null; }; } }; 'createTaxRate': { name: 'createTaxRate'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'TaxRate'; ofType: null; }; } }; 'createZone': { name: 'createZone'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Zone'; ofType: null; }; } }; 'deleteAdministrator': { name: 'deleteAdministrator'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deleteAdministrators': { name: 'deleteAdministrators'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; }; }; } }; 'deleteAsset': { name: 'deleteAsset'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deleteAssets': { name: 'deleteAssets'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deleteChannel': { name: 'deleteChannel'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deleteChannels': { name: 'deleteChannels'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; }; }; } }; 'deleteCollection': { name: 'deleteCollection'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deleteCollections': { name: 'deleteCollections'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; }; }; } }; 'deleteCountries': { name: 'deleteCountries'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; }; }; } }; 'deleteCountry': { name: 'deleteCountry'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deleteCustomer': { name: 'deleteCustomer'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deleteCustomerAddress': { name: 'deleteCustomerAddress'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Success'; ofType: null; }; } }; 'deleteCustomerGroup': { name: 'deleteCustomerGroup'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deleteCustomerGroups': { name: 'deleteCustomerGroups'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; }; }; } }; 'deleteCustomerNote': { name: 'deleteCustomerNote'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deleteCustomers': { name: 'deleteCustomers'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; }; }; } }; 'deleteDraftOrder': { name: 'deleteDraftOrder'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deleteFacet': { name: 'deleteFacet'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deleteFacetValues': { name: 'deleteFacetValues'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; }; }; } }; 'deleteFacets': { name: 'deleteFacets'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; }; }; } }; 'deleteOrderNote': { name: 'deleteOrderNote'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deletePaymentMethod': { name: 'deletePaymentMethod'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deletePaymentMethods': { name: 'deletePaymentMethods'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; }; }; } }; 'deleteProduct': { name: 'deleteProduct'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deleteProductOption': { name: 'deleteProductOption'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deleteProductVariant': { name: 'deleteProductVariant'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deleteProductVariants': { name: 'deleteProductVariants'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; }; }; } }; 'deleteProducts': { name: 'deleteProducts'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; }; }; } }; 'deletePromotion': { name: 'deletePromotion'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deletePromotions': { name: 'deletePromotions'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; }; }; } }; 'deleteProvince': { name: 'deleteProvince'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deleteRole': { name: 'deleteRole'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deleteRoles': { name: 'deleteRoles'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; }; }; } }; 'deleteSeller': { name: 'deleteSeller'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deleteSellers': { name: 'deleteSellers'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; }; }; } }; 'deleteShippingMethod': { name: 'deleteShippingMethod'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deleteShippingMethods': { name: 'deleteShippingMethods'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; }; }; } }; 'deleteStockLocation': { name: 'deleteStockLocation'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deleteStockLocations': { name: 'deleteStockLocations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; }; }; } }; 'deleteTag': { name: 'deleteTag'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deleteTaxCategories': { name: 'deleteTaxCategories'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; }; }; } }; 'deleteTaxCategory': { name: 'deleteTaxCategory'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deleteTaxRate': { name: 'deleteTaxRate'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deleteTaxRates': { name: 'deleteTaxRates'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; }; }; } }; 'deleteZone': { name: 'deleteZone'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; } }; 'deleteZones': { name: 'deleteZones'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'DeletionResponse'; ofType: null; }; }; }; } }; 'duplicateEntity': { name: 'duplicateEntity'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'DuplicateEntityResult'; ofType: null; }; } }; 'flushBufferedJobs': { name: 'flushBufferedJobs'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Success'; ofType: null; }; } }; 'importProducts': { name: 'importProducts'; type: { kind: 'OBJECT'; name: 'ImportInfo'; ofType: null; } }; 'login': { name: 'login'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'NativeAuthenticationResult'; ofType: null; }; } }; 'logout': { name: 'logout'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Success'; ofType: null; }; } }; 'modifyOrder': { name: 'modifyOrder'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'ModifyOrderResult'; ofType: null; }; } }; 'moveCollection': { name: 'moveCollection'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Collection'; ofType: null; }; } }; 'refundOrder': { name: 'refundOrder'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'RefundOrderResult'; ofType: null; }; } }; 'reindex': { name: 'reindex'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Job'; ofType: null; }; } }; 'removeCollectionsFromChannel': { name: 'removeCollectionsFromChannel'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Collection'; ofType: null; }; }; }; } }; 'removeCouponCodeFromDraftOrder': { name: 'removeCouponCodeFromDraftOrder'; type: { kind: 'OBJECT'; name: 'Order'; ofType: null; } }; 'removeCustomersFromGroup': { name: 'removeCustomersFromGroup'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'CustomerGroup'; ofType: null; }; } }; 'removeDraftOrderLine': { name: 'removeDraftOrderLine'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'RemoveOrderItemsResult'; ofType: null; }; } }; 'removeFacetsFromChannel': { name: 'removeFacetsFromChannel'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'RemoveFacetFromChannelResult'; ofType: null; }; }; }; } }; 'removeMembersFromZone': { name: 'removeMembersFromZone'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Zone'; ofType: null; }; } }; 'removeOptionGroupFromProduct': { name: 'removeOptionGroupFromProduct'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'RemoveOptionGroupFromProductResult'; ofType: null; }; } }; 'removePaymentMethodsFromChannel': { name: 'removePaymentMethodsFromChannel'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'PaymentMethod'; ofType: null; }; }; }; } }; 'removeProductVariantsFromChannel': { name: 'removeProductVariantsFromChannel'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductVariant'; ofType: null; }; }; }; } }; 'removeProductsFromChannel': { name: 'removeProductsFromChannel'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Product'; ofType: null; }; }; }; } }; 'removePromotionsFromChannel': { name: 'removePromotionsFromChannel'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Promotion'; ofType: null; }; }; }; } }; 'removeSettledJobs': { name: 'removeSettledJobs'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'removeShippingMethodsFromChannel': { name: 'removeShippingMethodsFromChannel'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ShippingMethod'; ofType: null; }; }; }; } }; 'removeStockLocationsFromChannel': { name: 'removeStockLocationsFromChannel'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'StockLocation'; ofType: null; }; }; }; } }; 'runPendingSearchIndexUpdates': { name: 'runPendingSearchIndexUpdates'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Success'; ofType: null; }; } }; 'runScheduledTask': { name: 'runScheduledTask'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Success'; ofType: null; }; } }; 'setCustomerForDraftOrder': { name: 'setCustomerForDraftOrder'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'SetCustomerForDraftOrderResult'; ofType: null; }; } }; 'setDraftOrderBillingAddress': { name: 'setDraftOrderBillingAddress'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Order'; ofType: null; }; } }; 'setDraftOrderCustomFields': { name: 'setDraftOrderCustomFields'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Order'; ofType: null; }; } }; 'setDraftOrderShippingAddress': { name: 'setDraftOrderShippingAddress'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Order'; ofType: null; }; } }; 'setDraftOrderShippingMethod': { name: 'setDraftOrderShippingMethod'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'SetOrderShippingMethodResult'; ofType: null; }; } }; 'setOrderCustomFields': { name: 'setOrderCustomFields'; type: { kind: 'OBJECT'; name: 'Order'; ofType: null; } }; 'setOrderCustomer': { name: 'setOrderCustomer'; type: { kind: 'OBJECT'; name: 'Order'; ofType: null; } }; 'settlePayment': { name: 'settlePayment'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'SettlePaymentResult'; ofType: null; }; } }; 'settleRefund': { name: 'settleRefund'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'SettleRefundResult'; ofType: null; }; } }; 'transitionFulfillmentToState': { name: 'transitionFulfillmentToState'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'TransitionFulfillmentToStateResult'; ofType: null; }; } }; 'transitionOrderToState': { name: 'transitionOrderToState'; type: { kind: 'UNION'; name: 'TransitionOrderToStateResult'; ofType: null; } }; 'transitionPaymentToState': { name: 'transitionPaymentToState'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'TransitionPaymentToStateResult'; ofType: null; }; } }; 'unsetDraftOrderBillingAddress': { name: 'unsetDraftOrderBillingAddress'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Order'; ofType: null; }; } }; 'unsetDraftOrderShippingAddress': { name: 'unsetDraftOrderShippingAddress'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Order'; ofType: null; }; } }; 'updateActiveAdministrator': { name: 'updateActiveAdministrator'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Administrator'; ofType: null; }; } }; 'updateAdministrator': { name: 'updateAdministrator'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Administrator'; ofType: null; }; } }; 'updateAsset': { name: 'updateAsset'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Asset'; ofType: null; }; } }; 'updateChannel': { name: 'updateChannel'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'UpdateChannelResult'; ofType: null; }; } }; 'updateCollection': { name: 'updateCollection'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Collection'; ofType: null; }; } }; 'updateCountry': { name: 'updateCountry'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Country'; ofType: null; }; } }; 'updateCustomer': { name: 'updateCustomer'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'UpdateCustomerResult'; ofType: null; }; } }; 'updateCustomerAddress': { name: 'updateCustomerAddress'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Address'; ofType: null; }; } }; 'updateCustomerGroup': { name: 'updateCustomerGroup'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'CustomerGroup'; ofType: null; }; } }; 'updateCustomerNote': { name: 'updateCustomerNote'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'HistoryEntry'; ofType: null; }; } }; 'updateFacet': { name: 'updateFacet'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Facet'; ofType: null; }; } }; 'updateFacetValues': { name: 'updateFacetValues'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'FacetValue'; ofType: null; }; }; }; } }; 'updateGlobalSettings': { name: 'updateGlobalSettings'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'UpdateGlobalSettingsResult'; ofType: null; }; } }; 'updateOrderNote': { name: 'updateOrderNote'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'HistoryEntry'; ofType: null; }; } }; 'updatePaymentMethod': { name: 'updatePaymentMethod'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'PaymentMethod'; ofType: null; }; } }; 'updateProduct': { name: 'updateProduct'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Product'; ofType: null; }; } }; 'updateProductOption': { name: 'updateProductOption'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductOption'; ofType: null; }; } }; 'updateProductOptionGroup': { name: 'updateProductOptionGroup'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductOptionGroup'; ofType: null; }; } }; 'updateProductVariant': { name: 'updateProductVariant'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductVariant'; ofType: null; }; } }; 'updateProductVariants': { name: 'updateProductVariants'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductVariant'; ofType: null; }; }; } }; 'updateProducts': { name: 'updateProducts'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Product'; ofType: null; }; }; }; } }; 'updatePromotion': { name: 'updatePromotion'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'UpdatePromotionResult'; ofType: null; }; } }; 'updateProvince': { name: 'updateProvince'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Province'; ofType: null; }; } }; 'updateRole': { name: 'updateRole'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Role'; ofType: null; }; } }; 'updateScheduledTask': { name: 'updateScheduledTask'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ScheduledTask'; ofType: null; }; } }; 'updateSeller': { name: 'updateSeller'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Seller'; ofType: null; }; } }; 'updateShippingMethod': { name: 'updateShippingMethod'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ShippingMethod'; ofType: null; }; } }; 'updateStockLocation': { name: 'updateStockLocation'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'StockLocation'; ofType: null; }; } }; 'updateTag': { name: 'updateTag'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Tag'; ofType: null; }; } }; 'updateTaxCategory': { name: 'updateTaxCategory'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'TaxCategory'; ofType: null; }; } }; 'updateTaxRate': { name: 'updateTaxRate'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'TaxRate'; ofType: null; }; } }; 'updateZone': { name: 'updateZone'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Zone'; ofType: null; }; } }; }; };
    'NativeAuthInput': { kind: 'INPUT_OBJECT'; name: 'NativeAuthInput'; isOneOf: false; inputFields: [{ name: 'username'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'password'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }]; };
    'NativeAuthStrategyError': { kind: 'OBJECT'; name: 'NativeAuthStrategyError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'NativeAuthenticationResult': { kind: 'UNION'; name: 'NativeAuthenticationResult'; fields: {}; possibleTypes: 'CurrentUser' | 'InvalidCredentialsError' | 'NativeAuthStrategyError'; };
    'NegativeQuantityError': { kind: 'OBJECT'; name: 'NegativeQuantityError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'NoActiveOrderError': { kind: 'OBJECT'; name: 'NoActiveOrderError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'NoChangesSpecifiedError': { kind: 'OBJECT'; name: 'NoChangesSpecifiedError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'Node': { kind: 'INTERFACE'; name: 'Node'; fields: { 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; }; possibleTypes: 'Address' | 'Administrator' | 'Allocation' | 'Asset' | 'AuthenticationMethod' | 'Cancellation' | 'Channel' | 'Collection' | 'Country' | 'Customer' | 'CustomerGroup' | 'Facet' | 'FacetValue' | 'Fulfillment' | 'HistoryEntry' | 'Job' | 'Order' | 'OrderLine' | 'OrderModification' | 'Payment' | 'PaymentMethod' | 'Product' | 'ProductOption' | 'ProductOptionGroup' | 'ProductVariant' | 'Promotion' | 'Province' | 'Refund' | 'Release' | 'Return' | 'Role' | 'Sale' | 'Seller' | 'ShippingMethod' | 'StockAdjustment' | 'StockLevel' | 'StockLocation' | 'Surcharge' | 'Tag' | 'TaxCategory' | 'TaxRate' | 'User' | 'Zone'; };
    'NothingToRefundError': { kind: 'OBJECT'; name: 'NothingToRefundError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'NumberListOperators': { kind: 'INPUT_OBJECT'; name: 'NumberListOperators'; isOneOf: false; inputFields: [{ name: 'inList'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; }; defaultValue: null }]; };
    'NumberOperators': { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; isOneOf: false; inputFields: [{ name: 'eq'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }, { name: 'lt'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }, { name: 'lte'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }, { name: 'gt'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }, { name: 'gte'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }, { name: 'between'; type: { kind: 'INPUT_OBJECT'; name: 'NumberRange'; ofType: null; }; defaultValue: null }, { name: 'isNull'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }]; };
    'NumberRange': { kind: 'INPUT_OBJECT'; name: 'NumberRange'; isOneOf: false; inputFields: [{ name: 'start'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; }; defaultValue: null }, { name: 'end'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; }; defaultValue: null }]; };
    'Order': { kind: 'OBJECT'; name: 'Order'; fields: { 'active': { name: 'active'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'aggregateOrder': { name: 'aggregateOrder'; type: { kind: 'OBJECT'; name: 'Order'; ofType: null; } }; 'aggregateOrderId': { name: 'aggregateOrderId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'billingAddress': { name: 'billingAddress'; type: { kind: 'OBJECT'; name: 'OrderAddress'; ofType: null; } }; 'channels': { name: 'channels'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Channel'; ofType: null; }; }; }; } }; 'code': { name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'couponCodes': { name: 'couponCodes'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; }; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'currencyCode': { name: 'currencyCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'CurrencyCode'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'customer': { name: 'customer'; type: { kind: 'OBJECT'; name: 'Customer'; ofType: null; } }; 'discounts': { name: 'discounts'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Discount'; ofType: null; }; }; }; } }; 'fulfillments': { name: 'fulfillments'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Fulfillment'; ofType: null; }; }; } }; 'history': { name: 'history'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'HistoryEntryList'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'lines': { name: 'lines'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'OrderLine'; ofType: null; }; }; }; } }; 'modifications': { name: 'modifications'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'OrderModification'; ofType: null; }; }; }; } }; 'nextStates': { name: 'nextStates'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; }; } }; 'orderPlacedAt': { name: 'orderPlacedAt'; type: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; } }; 'payments': { name: 'payments'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Payment'; ofType: null; }; }; } }; 'promotions': { name: 'promotions'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Promotion'; ofType: null; }; }; }; } }; 'sellerOrders': { name: 'sellerOrders'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Order'; ofType: null; }; }; } }; 'shipping': { name: 'shipping'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'shippingAddress': { name: 'shippingAddress'; type: { kind: 'OBJECT'; name: 'OrderAddress'; ofType: null; } }; 'shippingLines': { name: 'shippingLines'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ShippingLine'; ofType: null; }; }; }; } }; 'shippingWithTax': { name: 'shippingWithTax'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'state': { name: 'state'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'subTotal': { name: 'subTotal'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'subTotalWithTax': { name: 'subTotalWithTax'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'surcharges': { name: 'surcharges'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Surcharge'; ofType: null; }; }; }; } }; 'taxSummary': { name: 'taxSummary'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'OrderTaxSummary'; ofType: null; }; }; }; } }; 'total': { name: 'total'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'totalQuantity': { name: 'totalQuantity'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'totalWithTax': { name: 'totalWithTax'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'OrderType'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'OrderAddress': { kind: 'OBJECT'; name: 'OrderAddress'; fields: { 'city': { name: 'city'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'company': { name: 'company'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'country': { name: 'country'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'countryCode': { name: 'countryCode'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'fullName': { name: 'fullName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'phoneNumber': { name: 'phoneNumber'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'postalCode': { name: 'postalCode'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'province': { name: 'province'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'streetLine1': { name: 'streetLine1'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'streetLine2': { name: 'streetLine2'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'OrderFilterParameter': { kind: 'INPUT_OBJECT'; name: 'OrderFilterParameter'; isOneOf: false; inputFields: [{ name: 'customerLastName'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'transactionId'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'aggregateOrderId'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'type'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'orderPlacedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'code'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'state'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'active'; type: { kind: 'INPUT_OBJECT'; name: 'BooleanOperators'; ofType: null; }; defaultValue: null }, { name: 'totalQuantity'; type: { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; ofType: null; }; defaultValue: null }, { name: 'subTotal'; type: { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; ofType: null; }; defaultValue: null }, { name: 'subTotalWithTax'; type: { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; ofType: null; }; defaultValue: null }, { name: 'currencyCode'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'shipping'; type: { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; ofType: null; }; defaultValue: null }, { name: 'shippingWithTax'; type: { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; ofType: null; }; defaultValue: null }, { name: 'total'; type: { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; ofType: null; }; defaultValue: null }, { name: 'totalWithTax'; type: { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'OrderFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'OrderFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'OrderInterceptorError': { kind: 'OBJECT'; name: 'OrderInterceptorError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'interceptorError': { name: 'interceptorError'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'OrderLimitError': { kind: 'OBJECT'; name: 'OrderLimitError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'maxItems': { name: 'maxItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'OrderLine': { kind: 'OBJECT'; name: 'OrderLine'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'OBJECT'; name: 'OrderLineCustomFields'; ofType: null; } }; 'discountedLinePrice': { name: 'discountedLinePrice'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'discountedLinePriceWithTax': { name: 'discountedLinePriceWithTax'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'discountedUnitPrice': { name: 'discountedUnitPrice'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'discountedUnitPriceWithTax': { name: 'discountedUnitPriceWithTax'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'discounts': { name: 'discounts'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Discount'; ofType: null; }; }; }; } }; 'featuredAsset': { name: 'featuredAsset'; type: { kind: 'OBJECT'; name: 'Asset'; ofType: null; } }; 'fulfillmentLines': { name: 'fulfillmentLines'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'FulfillmentLine'; ofType: null; }; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'linePrice': { name: 'linePrice'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'linePriceWithTax': { name: 'linePriceWithTax'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'lineTax': { name: 'lineTax'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'order': { name: 'order'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Order'; ofType: null; }; } }; 'orderPlacedQuantity': { name: 'orderPlacedQuantity'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'productVariant': { name: 'productVariant'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductVariant'; ofType: null; }; } }; 'proratedLinePrice': { name: 'proratedLinePrice'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'proratedLinePriceWithTax': { name: 'proratedLinePriceWithTax'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'proratedUnitPrice': { name: 'proratedUnitPrice'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'proratedUnitPriceWithTax': { name: 'proratedUnitPriceWithTax'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'quantity': { name: 'quantity'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'taxLines': { name: 'taxLines'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'TaxLine'; ofType: null; }; }; }; } }; 'taxRate': { name: 'taxRate'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; } }; 'unitPrice': { name: 'unitPrice'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'unitPriceChangeSinceAdded': { name: 'unitPriceChangeSinceAdded'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'unitPriceWithTax': { name: 'unitPriceWithTax'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'unitPriceWithTaxChangeSinceAdded': { name: 'unitPriceWithTaxChangeSinceAdded'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'OrderLineCustomFields': { kind: 'OBJECT'; name: 'OrderLineCustomFields'; fields: { 'selectedDecals': { name: 'selectedDecals'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; }; };
    'OrderLineCustomFieldsInput': { kind: 'INPUT_OBJECT'; name: 'OrderLineCustomFieldsInput'; isOneOf: false; inputFields: [{ name: 'selectedDecals'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'OrderLineInput': { kind: 'INPUT_OBJECT'; name: 'OrderLineInput'; isOneOf: false; inputFields: [{ name: 'orderLineId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'quantity'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'INPUT_OBJECT'; name: 'OrderLineCustomFieldsInput'; ofType: null; }; defaultValue: null }]; };
    'OrderList': { kind: 'OBJECT'; name: 'OrderList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Order'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'OrderListOptions': { kind: 'INPUT_OBJECT'; name: 'OrderListOptions'; isOneOf: false; inputFields: [{ name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'OrderSortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'OrderFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'OrderModification': { kind: 'OBJECT'; name: 'OrderModification'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'isSettled': { name: 'isSettled'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'lines': { name: 'lines'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'OrderModificationLine'; ofType: null; }; }; }; } }; 'note': { name: 'note'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'payment': { name: 'payment'; type: { kind: 'OBJECT'; name: 'Payment'; ofType: null; } }; 'priceChange': { name: 'priceChange'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'refund': { name: 'refund'; type: { kind: 'OBJECT'; name: 'Refund'; ofType: null; } }; 'surcharges': { name: 'surcharges'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Surcharge'; ofType: null; }; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'OrderModificationError': { kind: 'OBJECT'; name: 'OrderModificationError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'OrderModificationLine': { kind: 'OBJECT'; name: 'OrderModificationLine'; fields: { 'modification': { name: 'modification'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'OrderModification'; ofType: null; }; } }; 'modificationId': { name: 'modificationId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'orderLine': { name: 'orderLine'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'OrderLine'; ofType: null; }; } }; 'orderLineId': { name: 'orderLineId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'quantity': { name: 'quantity'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'OrderModificationStateError': { kind: 'OBJECT'; name: 'OrderModificationStateError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'OrderProcessState': { kind: 'OBJECT'; name: 'OrderProcessState'; fields: { 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'to': { name: 'to'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; }; } }; }; };
    'OrderSortParameter': { kind: 'INPUT_OBJECT'; name: 'OrderSortParameter'; isOneOf: false; inputFields: [{ name: 'customerLastName'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'transactionId'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'aggregateOrderId'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'orderPlacedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'code'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'state'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'totalQuantity'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'subTotal'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'subTotalWithTax'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'shipping'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'shippingWithTax'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'total'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'totalWithTax'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'OrderStateTransitionError': { kind: 'OBJECT'; name: 'OrderStateTransitionError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'fromState': { name: 'fromState'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'toState': { name: 'toState'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'transitionError': { name: 'transitionError'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'OrderTaxSummary': { kind: 'OBJECT'; name: 'OrderTaxSummary'; fields: { 'description': { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'taxBase': { name: 'taxBase'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'taxRate': { name: 'taxRate'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; } }; 'taxTotal': { name: 'taxTotal'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; }; };
    'OrderType': { name: 'OrderType'; enumValues: 'Regular' | 'Seller' | 'Aggregate'; };
    'PaginatedList': { kind: 'INTERFACE'; name: 'PaginatedList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INTERFACE'; name: 'Node'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; possibleTypes: 'AdministratorList' | 'AssetList' | 'ChannelList' | 'CollectionList' | 'CountryList' | 'CustomerGroupList' | 'CustomerList' | 'FacetList' | 'FacetValueList' | 'HistoryEntryList' | 'JobList' | 'OrderList' | 'PaymentMethodList' | 'ProductList' | 'ProductVariantList' | 'PromotionList' | 'ProvinceList' | 'RoleList' | 'SellerList' | 'ShippingMethodList' | 'StockLocationList' | 'TagList' | 'TaxCategoryList' | 'TaxRateList' | 'ZoneList'; };
    'Payment': { kind: 'OBJECT'; name: 'Payment'; fields: { 'amount': { name: 'amount'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'errorMessage': { name: 'errorMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'metadata': { name: 'metadata'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'method': { name: 'method'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'nextStates': { name: 'nextStates'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; }; } }; 'refunds': { name: 'refunds'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Refund'; ofType: null; }; }; }; } }; 'state': { name: 'state'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'transactionId': { name: 'transactionId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'PaymentMethod': { kind: 'OBJECT'; name: 'PaymentMethod'; fields: { 'checker': { name: 'checker'; type: { kind: 'OBJECT'; name: 'ConfigurableOperation'; ofType: null; } }; 'code': { name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'enabled': { name: 'enabled'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'handler': { name: 'handler'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ConfigurableOperation'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'translations': { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'PaymentMethodTranslation'; ofType: null; }; }; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'PaymentMethodFilterParameter': { kind: 'INPUT_OBJECT'; name: 'PaymentMethodFilterParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'code'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'description'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'enabled'; type: { kind: 'INPUT_OBJECT'; name: 'BooleanOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'PaymentMethodFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'PaymentMethodFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'PaymentMethodList': { kind: 'OBJECT'; name: 'PaymentMethodList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'PaymentMethod'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'PaymentMethodListOptions': { kind: 'INPUT_OBJECT'; name: 'PaymentMethodListOptions'; isOneOf: false; inputFields: [{ name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'PaymentMethodSortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'PaymentMethodFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'PaymentMethodMissingError': { kind: 'OBJECT'; name: 'PaymentMethodMissingError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'PaymentMethodQuote': { kind: 'OBJECT'; name: 'PaymentMethodQuote'; fields: { 'code': { name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'eligibilityMessage': { name: 'eligibilityMessage'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'isEligible': { name: 'isEligible'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'PaymentMethodSortParameter': { kind: 'INPUT_OBJECT'; name: 'PaymentMethodSortParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'code'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'description'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'PaymentMethodTranslation': { kind: 'OBJECT'; name: 'PaymentMethodTranslation'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'description': { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'languageCode': { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'PaymentMethodTranslationInput': { kind: 'INPUT_OBJECT'; name: 'PaymentMethodTranslationInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'PaymentOrderMismatchError': { kind: 'OBJECT'; name: 'PaymentOrderMismatchError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'PaymentStateTransitionError': { kind: 'OBJECT'; name: 'PaymentStateTransitionError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'fromState': { name: 'fromState'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'toState': { name: 'toState'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'transitionError': { name: 'transitionError'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'Permission': { name: 'Permission'; enumValues: 'Authenticated' | 'SuperAdmin' | 'Owner' | 'Public' | 'UpdateGlobalSettings' | 'CreateCatalog' | 'ReadCatalog' | 'UpdateCatalog' | 'DeleteCatalog' | 'CreateSettings' | 'ReadSettings' | 'UpdateSettings' | 'DeleteSettings' | 'CreateAdministrator' | 'ReadAdministrator' | 'UpdateAdministrator' | 'DeleteAdministrator' | 'CreateAsset' | 'ReadAsset' | 'UpdateAsset' | 'DeleteAsset' | 'CreateChannel' | 'ReadChannel' | 'UpdateChannel' | 'DeleteChannel' | 'CreateCollection' | 'ReadCollection' | 'UpdateCollection' | 'DeleteCollection' | 'CreateCountry' | 'ReadCountry' | 'UpdateCountry' | 'DeleteCountry' | 'CreateCustomer' | 'ReadCustomer' | 'UpdateCustomer' | 'DeleteCustomer' | 'CreateCustomerGroup' | 'ReadCustomerGroup' | 'UpdateCustomerGroup' | 'DeleteCustomerGroup' | 'CreateFacet' | 'ReadFacet' | 'UpdateFacet' | 'DeleteFacet' | 'CreateOrder' | 'ReadOrder' | 'UpdateOrder' | 'DeleteOrder' | 'CreatePaymentMethod' | 'ReadPaymentMethod' | 'UpdatePaymentMethod' | 'DeletePaymentMethod' | 'CreateProduct' | 'ReadProduct' | 'UpdateProduct' | 'DeleteProduct' | 'CreatePromotion' | 'ReadPromotion' | 'UpdatePromotion' | 'DeletePromotion' | 'CreateShippingMethod' | 'ReadShippingMethod' | 'UpdateShippingMethod' | 'DeleteShippingMethod' | 'CreateTag' | 'ReadTag' | 'UpdateTag' | 'DeleteTag' | 'CreateTaxCategory' | 'ReadTaxCategory' | 'UpdateTaxCategory' | 'DeleteTaxCategory' | 'CreateTaxRate' | 'ReadTaxRate' | 'UpdateTaxRate' | 'DeleteTaxRate' | 'CreateSeller' | 'ReadSeller' | 'UpdateSeller' | 'DeleteSeller' | 'CreateStockLocation' | 'ReadStockLocation' | 'UpdateStockLocation' | 'DeleteStockLocation' | 'CreateSystem' | 'ReadSystem' | 'UpdateSystem' | 'DeleteSystem' | 'CreateZone' | 'ReadZone' | 'UpdateZone' | 'DeleteZone'; };
    'PermissionDefinition': { kind: 'OBJECT'; name: 'PermissionDefinition'; fields: { 'assignable': { name: 'assignable'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'description': { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'PreviewCollectionVariantsInput': { kind: 'INPUT_OBJECT'; name: 'PreviewCollectionVariantsInput'; isOneOf: false; inputFields: [{ name: 'parentId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'inheritFilters'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; }; defaultValue: null }, { name: 'filters'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ConfigurableOperationInput'; ofType: null; }; }; }; }; defaultValue: null }]; };
    'PriceRange': { kind: 'OBJECT'; name: 'PriceRange'; fields: { 'max': { name: 'max'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'min': { name: 'min'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; }; };
    'Product': { kind: 'OBJECT'; name: 'Product'; fields: { 'assets': { name: 'assets'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Asset'; ofType: null; }; }; }; } }; 'channels': { name: 'channels'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Channel'; ofType: null; }; }; }; } }; 'collections': { name: 'collections'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Collection'; ofType: null; }; }; }; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'enabled': { name: 'enabled'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'facetValues': { name: 'facetValues'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'FacetValue'; ofType: null; }; }; }; } }; 'featuredAsset': { name: 'featuredAsset'; type: { kind: 'OBJECT'; name: 'Asset'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'languageCode': { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'optionGroups': { name: 'optionGroups'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductOptionGroup'; ofType: null; }; }; }; } }; 'slug': { name: 'slug'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'translations': { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductTranslation'; ofType: null; }; }; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'variantList': { name: 'variantList'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductVariantList'; ofType: null; }; } }; 'variants': { name: 'variants'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductVariant'; ofType: null; }; }; }; } }; }; };
    'ProductFilterParameter': { kind: 'INPUT_OBJECT'; name: 'ProductFilterParameter'; isOneOf: false; inputFields: [{ name: 'facetValueId'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'sku'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'languageCode'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'slug'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'description'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'enabled'; type: { kind: 'INPUT_OBJECT'; name: 'BooleanOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ProductFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ProductFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'ProductList': { kind: 'OBJECT'; name: 'ProductList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Product'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'ProductListOptions': { kind: 'INPUT_OBJECT'; name: 'ProductListOptions'; isOneOf: false; inputFields: [{ name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'ProductSortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'ProductFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'ProductOption': { kind: 'OBJECT'; name: 'ProductOption'; fields: { 'code': { name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'group': { name: 'group'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductOptionGroup'; ofType: null; }; } }; 'groupId': { name: 'groupId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'languageCode': { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'translations': { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductOptionTranslation'; ofType: null; }; }; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'ProductOptionGroup': { kind: 'OBJECT'; name: 'ProductOptionGroup'; fields: { 'code': { name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'languageCode': { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'options': { name: 'options'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductOption'; ofType: null; }; }; }; } }; 'translations': { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductOptionGroupTranslation'; ofType: null; }; }; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'ProductOptionGroupTranslation': { kind: 'OBJECT'; name: 'ProductOptionGroupTranslation'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'languageCode': { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'ProductOptionGroupTranslationInput': { kind: 'INPUT_OBJECT'; name: 'ProductOptionGroupTranslationInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'ProductOptionInUseError': { kind: 'OBJECT'; name: 'ProductOptionInUseError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'optionGroupCode': { name: 'optionGroupCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'productVariantCount': { name: 'productVariantCount'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'ProductOptionTranslation': { kind: 'OBJECT'; name: 'ProductOptionTranslation'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'languageCode': { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'ProductOptionTranslationInput': { kind: 'INPUT_OBJECT'; name: 'ProductOptionTranslationInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'ProductSortParameter': { kind: 'INPUT_OBJECT'; name: 'ProductSortParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'slug'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'description'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'ProductTranslation': { kind: 'OBJECT'; name: 'ProductTranslation'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'description': { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'languageCode': { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'slug': { name: 'slug'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'ProductTranslationInput': { kind: 'INPUT_OBJECT'; name: 'ProductTranslationInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'slug'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'ProductVariant': { kind: 'OBJECT'; name: 'ProductVariant'; fields: { 'assets': { name: 'assets'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Asset'; ofType: null; }; }; }; } }; 'channels': { name: 'channels'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Channel'; ofType: null; }; }; }; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'currencyCode': { name: 'currencyCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'CurrencyCode'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'enabled': { name: 'enabled'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'facetValues': { name: 'facetValues'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'FacetValue'; ofType: null; }; }; }; } }; 'featuredAsset': { name: 'featuredAsset'; type: { kind: 'OBJECT'; name: 'Asset'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'languageCode': { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'options': { name: 'options'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductOption'; ofType: null; }; }; }; } }; 'outOfStockThreshold': { name: 'outOfStockThreshold'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'price': { name: 'price'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'priceWithTax': { name: 'priceWithTax'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'prices': { name: 'prices'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductVariantPrice'; ofType: null; }; }; }; } }; 'product': { name: 'product'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Product'; ofType: null; }; } }; 'productId': { name: 'productId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'sku': { name: 'sku'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'stockAllocated': { name: 'stockAllocated'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'stockLevel': { name: 'stockLevel'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'stockLevels': { name: 'stockLevels'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'StockLevel'; ofType: null; }; }; }; } }; 'stockMovements': { name: 'stockMovements'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'StockMovementList'; ofType: null; }; } }; 'stockOnHand': { name: 'stockOnHand'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'taxCategory': { name: 'taxCategory'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'TaxCategory'; ofType: null; }; } }; 'taxRateApplied': { name: 'taxRateApplied'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'TaxRate'; ofType: null; }; } }; 'trackInventory': { name: 'trackInventory'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'GlobalFlag'; ofType: null; }; } }; 'translations': { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductVariantTranslation'; ofType: null; }; }; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'useGlobalOutOfStockThreshold': { name: 'useGlobalOutOfStockThreshold'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; }; };
    'ProductVariantFilterParameter': { kind: 'INPUT_OBJECT'; name: 'ProductVariantFilterParameter'; isOneOf: false; inputFields: [{ name: 'facetValueId'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'enabled'; type: { kind: 'INPUT_OBJECT'; name: 'BooleanOperators'; ofType: null; }; defaultValue: null }, { name: 'trackInventory'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'stockOnHand'; type: { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; ofType: null; }; defaultValue: null }, { name: 'stockAllocated'; type: { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; ofType: null; }; defaultValue: null }, { name: 'outOfStockThreshold'; type: { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; ofType: null; }; defaultValue: null }, { name: 'useGlobalOutOfStockThreshold'; type: { kind: 'INPUT_OBJECT'; name: 'BooleanOperators'; ofType: null; }; defaultValue: null }, { name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'productId'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'languageCode'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'sku'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'price'; type: { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; ofType: null; }; defaultValue: null }, { name: 'currencyCode'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'priceWithTax'; type: { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; ofType: null; }; defaultValue: null }, { name: 'stockLevel'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ProductVariantFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ProductVariantFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'ProductVariantList': { kind: 'OBJECT'; name: 'ProductVariantList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductVariant'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'ProductVariantListOptions': { kind: 'INPUT_OBJECT'; name: 'ProductVariantListOptions'; isOneOf: false; inputFields: [{ name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'ProductVariantSortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'ProductVariantFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'ProductVariantPrice': { kind: 'OBJECT'; name: 'ProductVariantPrice'; fields: { 'currencyCode': { name: 'currencyCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'CurrencyCode'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'price': { name: 'price'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; }; };
    'ProductVariantSortParameter': { kind: 'INPUT_OBJECT'; name: 'ProductVariantSortParameter'; isOneOf: false; inputFields: [{ name: 'stockOnHand'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'stockAllocated'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'outOfStockThreshold'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'productId'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'sku'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'price'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'priceWithTax'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'stockLevel'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'ProductVariantTranslation': { kind: 'OBJECT'; name: 'ProductVariantTranslation'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'languageCode': { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'ProductVariantTranslationInput': { kind: 'INPUT_OBJECT'; name: 'ProductVariantTranslationInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'Promotion': { kind: 'OBJECT'; name: 'Promotion'; fields: { 'actions': { name: 'actions'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ConfigurableOperation'; ofType: null; }; }; }; } }; 'conditions': { name: 'conditions'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ConfigurableOperation'; ofType: null; }; }; }; } }; 'couponCode': { name: 'couponCode'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'enabled': { name: 'enabled'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'endsAt': { name: 'endsAt'; type: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'perCustomerUsageLimit': { name: 'perCustomerUsageLimit'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'startsAt': { name: 'startsAt'; type: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; } }; 'translations': { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'PromotionTranslation'; ofType: null; }; }; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'usageLimit': { name: 'usageLimit'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; }; };
    'PromotionFilterParameter': { kind: 'INPUT_OBJECT'; name: 'PromotionFilterParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'startsAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'endsAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'couponCode'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'perCustomerUsageLimit'; type: { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; ofType: null; }; defaultValue: null }, { name: 'usageLimit'; type: { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'description'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'enabled'; type: { kind: 'INPUT_OBJECT'; name: 'BooleanOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'PromotionFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'PromotionFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'PromotionList': { kind: 'OBJECT'; name: 'PromotionList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Promotion'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'PromotionListOptions': { kind: 'INPUT_OBJECT'; name: 'PromotionListOptions'; isOneOf: false; inputFields: [{ name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'PromotionSortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'PromotionFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'PromotionSortParameter': { kind: 'INPUT_OBJECT'; name: 'PromotionSortParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'startsAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'endsAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'couponCode'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'perCustomerUsageLimit'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'usageLimit'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'description'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'PromotionTranslation': { kind: 'OBJECT'; name: 'PromotionTranslation'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'description': { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'languageCode': { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'PromotionTranslationInput': { kind: 'INPUT_OBJECT'; name: 'PromotionTranslationInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'Province': { kind: 'OBJECT'; name: 'Province'; fields: { 'code': { name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'enabled': { name: 'enabled'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'languageCode': { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'parent': { name: 'parent'; type: { kind: 'INTERFACE'; name: 'Region'; ofType: null; } }; 'parentId': { name: 'parentId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'translations': { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'RegionTranslation'; ofType: null; }; }; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'ProvinceFilterParameter': { kind: 'INPUT_OBJECT'; name: 'ProvinceFilterParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'languageCode'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'code'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'type'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'enabled'; type: { kind: 'INPUT_OBJECT'; name: 'BooleanOperators'; ofType: null; }; defaultValue: null }, { name: 'parentId'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ProvinceFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ProvinceFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'ProvinceList': { kind: 'OBJECT'; name: 'ProvinceList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Province'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'ProvinceListOptions': { kind: 'INPUT_OBJECT'; name: 'ProvinceListOptions'; isOneOf: false; inputFields: [{ name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'ProvinceSortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'ProvinceFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'ProvinceSortParameter': { kind: 'INPUT_OBJECT'; name: 'ProvinceSortParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'code'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'type'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'parentId'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'ProvinceTranslationInput': { kind: 'INPUT_OBJECT'; name: 'ProvinceTranslationInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'QuantityTooGreatError': { kind: 'OBJECT'; name: 'QuantityTooGreatError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'Query': { kind: 'OBJECT'; name: 'Query'; fields: { 'activeAdministrator': { name: 'activeAdministrator'; type: { kind: 'OBJECT'; name: 'Administrator'; ofType: null; } }; 'activeChannel': { name: 'activeChannel'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Channel'; ofType: null; }; } }; 'administrator': { name: 'administrator'; type: { kind: 'OBJECT'; name: 'Administrator'; ofType: null; } }; 'administrators': { name: 'administrators'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'AdministratorList'; ofType: null; }; } }; 'asset': { name: 'asset'; type: { kind: 'OBJECT'; name: 'Asset'; ofType: null; } }; 'assets': { name: 'assets'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'AssetList'; ofType: null; }; } }; 'channel': { name: 'channel'; type: { kind: 'OBJECT'; name: 'Channel'; ofType: null; } }; 'channels': { name: 'channels'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ChannelList'; ofType: null; }; } }; 'collection': { name: 'collection'; type: { kind: 'OBJECT'; name: 'Collection'; ofType: null; } }; 'collectionFilters': { name: 'collectionFilters'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ConfigurableOperationDefinition'; ofType: null; }; }; }; } }; 'collections': { name: 'collections'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'CollectionList'; ofType: null; }; } }; 'countries': { name: 'countries'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'CountryList'; ofType: null; }; } }; 'country': { name: 'country'; type: { kind: 'OBJECT'; name: 'Country'; ofType: null; } }; 'customer': { name: 'customer'; type: { kind: 'OBJECT'; name: 'Customer'; ofType: null; } }; 'customerGroup': { name: 'customerGroup'; type: { kind: 'OBJECT'; name: 'CustomerGroup'; ofType: null; } }; 'customerGroups': { name: 'customerGroups'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'CustomerGroupList'; ofType: null; }; } }; 'customers': { name: 'customers'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'CustomerList'; ofType: null; }; } }; 'eligibleShippingMethodsForDraftOrder': { name: 'eligibleShippingMethodsForDraftOrder'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ShippingMethodQuote'; ofType: null; }; }; }; } }; 'entityDuplicators': { name: 'entityDuplicators'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'EntityDuplicatorDefinition'; ofType: null; }; }; }; } }; 'facet': { name: 'facet'; type: { kind: 'OBJECT'; name: 'Facet'; ofType: null; } }; 'facetValues': { name: 'facetValues'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'FacetValueList'; ofType: null; }; } }; 'facets': { name: 'facets'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'FacetList'; ofType: null; }; } }; 'fulfillmentHandlers': { name: 'fulfillmentHandlers'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ConfigurableOperationDefinition'; ofType: null; }; }; }; } }; 'globalSettings': { name: 'globalSettings'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'GlobalSettings'; ofType: null; }; } }; 'job': { name: 'job'; type: { kind: 'OBJECT'; name: 'Job'; ofType: null; } }; 'jobBufferSize': { name: 'jobBufferSize'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'JobBufferSize'; ofType: null; }; }; }; } }; 'jobQueues': { name: 'jobQueues'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'JobQueue'; ofType: null; }; }; }; } }; 'jobs': { name: 'jobs'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'JobList'; ofType: null; }; } }; 'jobsById': { name: 'jobsById'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Job'; ofType: null; }; }; }; } }; 'me': { name: 'me'; type: { kind: 'OBJECT'; name: 'CurrentUser'; ofType: null; } }; 'metricSummary': { name: 'metricSummary'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'MetricSummary'; ofType: null; }; }; }; } }; 'order': { name: 'order'; type: { kind: 'OBJECT'; name: 'Order'; ofType: null; } }; 'orders': { name: 'orders'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'OrderList'; ofType: null; }; } }; 'paymentMethod': { name: 'paymentMethod'; type: { kind: 'OBJECT'; name: 'PaymentMethod'; ofType: null; } }; 'paymentMethodEligibilityCheckers': { name: 'paymentMethodEligibilityCheckers'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ConfigurableOperationDefinition'; ofType: null; }; }; }; } }; 'paymentMethodHandlers': { name: 'paymentMethodHandlers'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ConfigurableOperationDefinition'; ofType: null; }; }; }; } }; 'paymentMethods': { name: 'paymentMethods'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'PaymentMethodList'; ofType: null; }; } }; 'pendingSearchIndexUpdates': { name: 'pendingSearchIndexUpdates'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'previewCollectionVariants': { name: 'previewCollectionVariants'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductVariantList'; ofType: null; }; } }; 'product': { name: 'product'; type: { kind: 'OBJECT'; name: 'Product'; ofType: null; } }; 'productOptionGroup': { name: 'productOptionGroup'; type: { kind: 'OBJECT'; name: 'ProductOptionGroup'; ofType: null; } }; 'productOptionGroups': { name: 'productOptionGroups'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductOptionGroup'; ofType: null; }; }; }; } }; 'productVariant': { name: 'productVariant'; type: { kind: 'OBJECT'; name: 'ProductVariant'; ofType: null; } }; 'productVariants': { name: 'productVariants'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductVariantList'; ofType: null; }; } }; 'products': { name: 'products'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductList'; ofType: null; }; } }; 'promotion': { name: 'promotion'; type: { kind: 'OBJECT'; name: 'Promotion'; ofType: null; } }; 'promotionActions': { name: 'promotionActions'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ConfigurableOperationDefinition'; ofType: null; }; }; }; } }; 'promotionConditions': { name: 'promotionConditions'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ConfigurableOperationDefinition'; ofType: null; }; }; }; } }; 'promotions': { name: 'promotions'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'PromotionList'; ofType: null; }; } }; 'province': { name: 'province'; type: { kind: 'OBJECT'; name: 'Province'; ofType: null; } }; 'provinces': { name: 'provinces'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProvinceList'; ofType: null; }; } }; 'role': { name: 'role'; type: { kind: 'OBJECT'; name: 'Role'; ofType: null; } }; 'roles': { name: 'roles'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'RoleList'; ofType: null; }; } }; 'scheduledTasks': { name: 'scheduledTasks'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ScheduledTask'; ofType: null; }; }; }; } }; 'search': { name: 'search'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'SearchResponse'; ofType: null; }; } }; 'seller': { name: 'seller'; type: { kind: 'OBJECT'; name: 'Seller'; ofType: null; } }; 'sellers': { name: 'sellers'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'SellerList'; ofType: null; }; } }; 'shippingCalculators': { name: 'shippingCalculators'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ConfigurableOperationDefinition'; ofType: null; }; }; }; } }; 'shippingEligibilityCheckers': { name: 'shippingEligibilityCheckers'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ConfigurableOperationDefinition'; ofType: null; }; }; }; } }; 'shippingMethod': { name: 'shippingMethod'; type: { kind: 'OBJECT'; name: 'ShippingMethod'; ofType: null; } }; 'shippingMethods': { name: 'shippingMethods'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ShippingMethodList'; ofType: null; }; } }; 'stockLocation': { name: 'stockLocation'; type: { kind: 'OBJECT'; name: 'StockLocation'; ofType: null; } }; 'stockLocations': { name: 'stockLocations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'StockLocationList'; ofType: null; }; } }; 'tag': { name: 'tag'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Tag'; ofType: null; }; } }; 'tags': { name: 'tags'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'TagList'; ofType: null; }; } }; 'taxCategories': { name: 'taxCategories'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'TaxCategoryList'; ofType: null; }; } }; 'taxCategory': { name: 'taxCategory'; type: { kind: 'OBJECT'; name: 'TaxCategory'; ofType: null; } }; 'taxRate': { name: 'taxRate'; type: { kind: 'OBJECT'; name: 'TaxRate'; ofType: null; } }; 'taxRates': { name: 'taxRates'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'TaxRateList'; ofType: null; }; } }; 'testEligibleShippingMethods': { name: 'testEligibleShippingMethods'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ShippingMethodQuote'; ofType: null; }; }; }; } }; 'testShippingMethod': { name: 'testShippingMethod'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'TestShippingMethodResult'; ofType: null; }; } }; 'zone': { name: 'zone'; type: { kind: 'OBJECT'; name: 'Zone'; ofType: null; } }; 'zones': { name: 'zones'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ZoneList'; ofType: null; }; } }; }; };
    'Refund': { kind: 'OBJECT'; name: 'Refund'; fields: { 'adjustment': { name: 'adjustment'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'lines': { name: 'lines'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'RefundLine'; ofType: null; }; }; }; } }; 'metadata': { name: 'metadata'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'method': { name: 'method'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'paymentId': { name: 'paymentId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'reason': { name: 'reason'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'shipping': { name: 'shipping'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'state': { name: 'state'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'total': { name: 'total'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'transactionId': { name: 'transactionId'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'RefundAmountError': { kind: 'OBJECT'; name: 'RefundAmountError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'maximumRefundable': { name: 'maximumRefundable'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'RefundLine': { kind: 'OBJECT'; name: 'RefundLine'; fields: { 'orderLine': { name: 'orderLine'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'OrderLine'; ofType: null; }; } }; 'orderLineId': { name: 'orderLineId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'quantity': { name: 'quantity'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'refund': { name: 'refund'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Refund'; ofType: null; }; } }; 'refundId': { name: 'refundId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; }; };
    'RefundOrderInput': { kind: 'INPUT_OBJECT'; name: 'RefundOrderInput'; isOneOf: false; inputFields: [{ name: 'lines'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'OrderLineInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'shipping'; type: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; defaultValue: null }, { name: 'adjustment'; type: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; defaultValue: null }, { name: 'amount'; type: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; defaultValue: null }, { name: 'paymentId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'reason'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'RefundOrderResult': { kind: 'UNION'; name: 'RefundOrderResult'; fields: {}; possibleTypes: 'AlreadyRefundedError' | 'MultipleOrderError' | 'NothingToRefundError' | 'OrderStateTransitionError' | 'PaymentOrderMismatchError' | 'QuantityTooGreatError' | 'Refund' | 'RefundAmountError' | 'RefundOrderStateError' | 'RefundStateTransitionError'; };
    'RefundOrderStateError': { kind: 'OBJECT'; name: 'RefundOrderStateError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'orderState': { name: 'orderState'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'RefundPaymentIdMissingError': { kind: 'OBJECT'; name: 'RefundPaymentIdMissingError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'RefundStateTransitionError': { kind: 'OBJECT'; name: 'RefundStateTransitionError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'fromState': { name: 'fromState'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'toState': { name: 'toState'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'transitionError': { name: 'transitionError'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'Region': { kind: 'INTERFACE'; name: 'Region'; fields: { 'code': { name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'enabled': { name: 'enabled'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'languageCode': { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'parent': { name: 'parent'; type: { kind: 'INTERFACE'; name: 'Region'; ofType: null; } }; 'parentId': { name: 'parentId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; } }; 'translations': { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'RegionTranslation'; ofType: null; }; }; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; possibleTypes: 'Country' | 'Province'; };
    'RegionTranslation': { kind: 'OBJECT'; name: 'RegionTranslation'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'languageCode': { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'RelationCustomFieldConfig': { kind: 'OBJECT'; name: 'RelationCustomFieldConfig'; fields: { 'description': { name: 'description'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'entity': { name: 'entity'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'internal': { name: 'internal'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'list': { name: 'list'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'nullable': { name: 'nullable'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'readonly': { name: 'readonly'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiresPermission': { name: 'requiresPermission'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; } }; 'scalarFields': { name: 'scalarFields'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'ui': { name: 'ui'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; };
    'Release': { kind: 'OBJECT'; name: 'Release'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'productVariant': { name: 'productVariant'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductVariant'; ofType: null; }; } }; 'quantity': { name: 'quantity'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'StockMovementType'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'RemoveCollectionsFromChannelInput': { kind: 'INPUT_OBJECT'; name: 'RemoveCollectionsFromChannelInput'; isOneOf: false; inputFields: [{ name: 'collectionIds'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'channelId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }]; };
    'RemoveFacetFromChannelResult': { kind: 'UNION'; name: 'RemoveFacetFromChannelResult'; fields: {}; possibleTypes: 'Facet' | 'FacetInUseError'; };
    'RemoveFacetsFromChannelInput': { kind: 'INPUT_OBJECT'; name: 'RemoveFacetsFromChannelInput'; isOneOf: false; inputFields: [{ name: 'facetIds'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'channelId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'force'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }]; };
    'RemoveOptionGroupFromProductResult': { kind: 'UNION'; name: 'RemoveOptionGroupFromProductResult'; fields: {}; possibleTypes: 'Product' | 'ProductOptionInUseError'; };
    'RemoveOrderItemsResult': { kind: 'UNION'; name: 'RemoveOrderItemsResult'; fields: {}; possibleTypes: 'Order' | 'OrderInterceptorError' | 'OrderModificationError'; };
    'RemovePaymentMethodsFromChannelInput': { kind: 'INPUT_OBJECT'; name: 'RemovePaymentMethodsFromChannelInput'; isOneOf: false; inputFields: [{ name: 'paymentMethodIds'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'channelId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }]; };
    'RemoveProductVariantsFromChannelInput': { kind: 'INPUT_OBJECT'; name: 'RemoveProductVariantsFromChannelInput'; isOneOf: false; inputFields: [{ name: 'productVariantIds'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'channelId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }]; };
    'RemoveProductsFromChannelInput': { kind: 'INPUT_OBJECT'; name: 'RemoveProductsFromChannelInput'; isOneOf: false; inputFields: [{ name: 'productIds'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'channelId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }]; };
    'RemovePromotionsFromChannelInput': { kind: 'INPUT_OBJECT'; name: 'RemovePromotionsFromChannelInput'; isOneOf: false; inputFields: [{ name: 'promotionIds'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'channelId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }]; };
    'RemoveShippingMethodsFromChannelInput': { kind: 'INPUT_OBJECT'; name: 'RemoveShippingMethodsFromChannelInput'; isOneOf: false; inputFields: [{ name: 'shippingMethodIds'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'channelId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }]; };
    'RemoveStockLocationsFromChannelInput': { kind: 'INPUT_OBJECT'; name: 'RemoveStockLocationsFromChannelInput'; isOneOf: false; inputFields: [{ name: 'stockLocationIds'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'channelId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }]; };
    'Return': { kind: 'OBJECT'; name: 'Return'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'productVariant': { name: 'productVariant'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductVariant'; ofType: null; }; } }; 'quantity': { name: 'quantity'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'StockMovementType'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'Role': { kind: 'OBJECT'; name: 'Role'; fields: { 'channels': { name: 'channels'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Channel'; ofType: null; }; }; }; } }; 'code': { name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'description': { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'permissions': { name: 'permissions'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'RoleFilterParameter': { kind: 'INPUT_OBJECT'; name: 'RoleFilterParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'code'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'description'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'RoleFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'RoleFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'RoleList': { kind: 'OBJECT'; name: 'RoleList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Role'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'RoleListOptions': { kind: 'INPUT_OBJECT'; name: 'RoleListOptions'; isOneOf: false; inputFields: [{ name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'RoleSortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'RoleFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'RoleSortParameter': { kind: 'INPUT_OBJECT'; name: 'RoleSortParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'code'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'description'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'Sale': { kind: 'OBJECT'; name: 'Sale'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'productVariant': { name: 'productVariant'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductVariant'; ofType: null; }; } }; 'quantity': { name: 'quantity'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'StockMovementType'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'ScheduledTask': { kind: 'OBJECT'; name: 'ScheduledTask'; fields: { 'description': { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'enabled': { name: 'enabled'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'isRunning': { name: 'isRunning'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'lastExecutedAt': { name: 'lastExecutedAt'; type: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; } }; 'lastResult': { name: 'lastResult'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'nextExecutionAt': { name: 'nextExecutionAt'; type: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; } }; 'schedule': { name: 'schedule'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'scheduleDescription': { name: 'scheduleDescription'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'SearchInput': { kind: 'INPUT_OBJECT'; name: 'SearchInput'; isOneOf: false; inputFields: [{ name: 'term'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'facetValueIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; defaultValue: null }, { name: 'facetValueOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }, { name: 'facetValueFilters'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'FacetValueFilterInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'collectionId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'collectionSlug'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'groupByProduct'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'SearchResultSortParameter'; ofType: null; }; defaultValue: null }, { name: 'inStock'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }]; };
    'SearchReindexResponse': { kind: 'OBJECT'; name: 'SearchReindexResponse'; fields: { 'success': { name: 'success'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; }; };
    'SearchResponse': { kind: 'OBJECT'; name: 'SearchResponse'; fields: { 'collections': { name: 'collections'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'CollectionResult'; ofType: null; }; }; }; } }; 'facetValues': { name: 'facetValues'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'FacetValueResult'; ofType: null; }; }; }; } }; 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'SearchResult'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'SearchResult': { kind: 'OBJECT'; name: 'SearchResult'; fields: { 'channelIds': { name: 'channelIds'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; } }; 'collectionIds': { name: 'collectionIds'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; } }; 'currencyCode': { name: 'currencyCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'CurrencyCode'; ofType: null; }; } }; 'description': { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'enabled': { name: 'enabled'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'facetIds': { name: 'facetIds'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; } }; 'facetValueIds': { name: 'facetValueIds'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; } }; 'inStock': { name: 'inStock'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'price': { name: 'price'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'SearchResultPrice'; ofType: null; }; } }; 'priceWithTax': { name: 'priceWithTax'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'SearchResultPrice'; ofType: null; }; } }; 'productAsset': { name: 'productAsset'; type: { kind: 'OBJECT'; name: 'SearchResultAsset'; ofType: null; } }; 'productId': { name: 'productId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'productName': { name: 'productName'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'productVariantAsset': { name: 'productVariantAsset'; type: { kind: 'OBJECT'; name: 'SearchResultAsset'; ofType: null; } }; 'productVariantId': { name: 'productVariantId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'productVariantName': { name: 'productVariantName'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'score': { name: 'score'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; } }; 'sku': { name: 'sku'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'slug': { name: 'slug'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'SearchResultAsset': { kind: 'OBJECT'; name: 'SearchResultAsset'; fields: { 'focalPoint': { name: 'focalPoint'; type: { kind: 'OBJECT'; name: 'Coordinate'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'preview': { name: 'preview'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'SearchResultPrice': { kind: 'UNION'; name: 'SearchResultPrice'; fields: {}; possibleTypes: 'PriceRange' | 'SinglePrice'; };
    'SearchResultSortParameter': { kind: 'INPUT_OBJECT'; name: 'SearchResultSortParameter'; isOneOf: false; inputFields: [{ name: 'name'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'price'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'Seller': { kind: 'OBJECT'; name: 'Seller'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'SellerFilterParameter': { kind: 'INPUT_OBJECT'; name: 'SellerFilterParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'SellerFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'SellerFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'SellerList': { kind: 'OBJECT'; name: 'SellerList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Seller'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'SellerListOptions': { kind: 'INPUT_OBJECT'; name: 'SellerListOptions'; isOneOf: false; inputFields: [{ name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'SellerSortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'SellerFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'SellerSortParameter': { kind: 'INPUT_OBJECT'; name: 'SellerSortParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'ServerConfig': { kind: 'OBJECT'; name: 'ServerConfig'; fields: { 'customFieldConfig': { name: 'customFieldConfig'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'CustomFields'; ofType: null; }; } }; 'entityCustomFields': { name: 'entityCustomFields'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'EntityCustomFields'; ofType: null; }; }; }; } }; 'moneyStrategyPrecision': { name: 'moneyStrategyPrecision'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'orderProcess': { name: 'orderProcess'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'OrderProcessState'; ofType: null; }; }; }; } }; 'permissions': { name: 'permissions'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'PermissionDefinition'; ofType: null; }; }; }; } }; 'permittedAssetTypes': { name: 'permittedAssetTypes'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; }; } }; }; };
    'SetCustomerForDraftOrderResult': { kind: 'UNION'; name: 'SetCustomerForDraftOrderResult'; fields: {}; possibleTypes: 'EmailAddressConflictError' | 'Order'; };
    'SetOrderCustomerInput': { kind: 'INPUT_OBJECT'; name: 'SetOrderCustomerInput'; isOneOf: false; inputFields: [{ name: 'orderId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'customerId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'note'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'SetOrderShippingMethodResult': { kind: 'UNION'; name: 'SetOrderShippingMethodResult'; fields: {}; possibleTypes: 'IneligibleShippingMethodError' | 'NoActiveOrderError' | 'Order' | 'OrderModificationError'; };
    'SettlePaymentError': { kind: 'OBJECT'; name: 'SettlePaymentError'; fields: { 'errorCode': { name: 'errorCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'ErrorCode'; ofType: null; }; } }; 'message': { name: 'message'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'paymentErrorMessage': { name: 'paymentErrorMessage'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'SettlePaymentResult': { kind: 'UNION'; name: 'SettlePaymentResult'; fields: {}; possibleTypes: 'OrderStateTransitionError' | 'Payment' | 'PaymentStateTransitionError' | 'SettlePaymentError'; };
    'SettleRefundInput': { kind: 'INPUT_OBJECT'; name: 'SettleRefundInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'transactionId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }]; };
    'SettleRefundResult': { kind: 'UNION'; name: 'SettleRefundResult'; fields: {}; possibleTypes: 'Refund' | 'RefundStateTransitionError'; };
    'ShippingLine': { kind: 'OBJECT'; name: 'ShippingLine'; fields: { 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'discountedPrice': { name: 'discountedPrice'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'discountedPriceWithTax': { name: 'discountedPriceWithTax'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'discounts': { name: 'discounts'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Discount'; ofType: null; }; }; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'price': { name: 'price'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'priceWithTax': { name: 'priceWithTax'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'shippingMethod': { name: 'shippingMethod'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ShippingMethod'; ofType: null; }; } }; }; };
    'ShippingMethod': { kind: 'OBJECT'; name: 'ShippingMethod'; fields: { 'calculator': { name: 'calculator'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ConfigurableOperation'; ofType: null; }; } }; 'checker': { name: 'checker'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ConfigurableOperation'; ofType: null; }; } }; 'code': { name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'fulfillmentHandlerCode': { name: 'fulfillmentHandlerCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'languageCode': { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'translations': { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ShippingMethodTranslation'; ofType: null; }; }; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'ShippingMethodFilterParameter': { kind: 'INPUT_OBJECT'; name: 'ShippingMethodFilterParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'languageCode'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'code'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'description'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'fulfillmentHandlerCode'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ShippingMethodFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ShippingMethodFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'ShippingMethodList': { kind: 'OBJECT'; name: 'ShippingMethodList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ShippingMethod'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'ShippingMethodListOptions': { kind: 'INPUT_OBJECT'; name: 'ShippingMethodListOptions'; isOneOf: false; inputFields: [{ name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'ShippingMethodSortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'ShippingMethodFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'ShippingMethodQuote': { kind: 'OBJECT'; name: 'ShippingMethodQuote'; fields: { 'code': { name: 'code'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'metadata': { name: 'metadata'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'price': { name: 'price'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'priceWithTax': { name: 'priceWithTax'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; }; };
    'ShippingMethodSortParameter': { kind: 'INPUT_OBJECT'; name: 'ShippingMethodSortParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'code'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'description'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'fulfillmentHandlerCode'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'ShippingMethodTranslation': { kind: 'OBJECT'; name: 'ShippingMethodTranslation'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'description': { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'languageCode': { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'ShippingMethodTranslationInput': { kind: 'INPUT_OBJECT'; name: 'ShippingMethodTranslationInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'SinglePrice': { kind: 'OBJECT'; name: 'SinglePrice'; fields: { 'value': { name: 'value'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; }; };
    'SortOrder': { name: 'SortOrder'; enumValues: 'ASC' | 'DESC'; };
    'StockAdjustment': { kind: 'OBJECT'; name: 'StockAdjustment'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'productVariant': { name: 'productVariant'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductVariant'; ofType: null; }; } }; 'quantity': { name: 'quantity'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'StockMovementType'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'StockLevel': { kind: 'OBJECT'; name: 'StockLevel'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'stockAllocated': { name: 'stockAllocated'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'stockLocation': { name: 'stockLocation'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'StockLocation'; ofType: null; }; } }; 'stockLocationId': { name: 'stockLocationId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'stockOnHand': { name: 'stockOnHand'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'StockLevelInput': { kind: 'INPUT_OBJECT'; name: 'StockLevelInput'; isOneOf: false; inputFields: [{ name: 'stockLocationId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'stockOnHand'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; }; defaultValue: null }]; };
    'StockLocation': { kind: 'OBJECT'; name: 'StockLocation'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'description': { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'StockLocationFilterParameter': { kind: 'INPUT_OBJECT'; name: 'StockLocationFilterParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'description'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'StockLocationFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'StockLocationFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'StockLocationList': { kind: 'OBJECT'; name: 'StockLocationList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'StockLocation'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'StockLocationListOptions': { kind: 'INPUT_OBJECT'; name: 'StockLocationListOptions'; isOneOf: false; inputFields: [{ name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'StockLocationSortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'StockLocationFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'StockLocationSortParameter': { kind: 'INPUT_OBJECT'; name: 'StockLocationSortParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'description'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'StockMovement': { kind: 'INTERFACE'; name: 'StockMovement'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'productVariant': { name: 'productVariant'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'ProductVariant'; ofType: null; }; } }; 'quantity': { name: 'quantity'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'StockMovementType'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; possibleTypes: 'Allocation' | 'Cancellation' | 'Release' | 'Return' | 'Sale' | 'StockAdjustment'; };
    'StockMovementItem': { kind: 'UNION'; name: 'StockMovementItem'; fields: {}; possibleTypes: 'Allocation' | 'Cancellation' | 'Release' | 'Return' | 'Sale' | 'StockAdjustment'; };
    'StockMovementList': { kind: 'OBJECT'; name: 'StockMovementList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'StockMovementItem'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'StockMovementListOptions': { kind: 'INPUT_OBJECT'; name: 'StockMovementListOptions'; isOneOf: false; inputFields: [{ name: 'type'; type: { kind: 'ENUM'; name: 'StockMovementType'; ofType: null; }; defaultValue: null }, { name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }]; };
    'StockMovementType': { name: 'StockMovementType'; enumValues: 'ADJUSTMENT' | 'ALLOCATION' | 'RELEASE' | 'SALE' | 'CANCELLATION' | 'RETURN'; };
    'String': unknown;
    'StringCustomFieldConfig': { kind: 'OBJECT'; name: 'StringCustomFieldConfig'; fields: { 'description': { name: 'description'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'internal': { name: 'internal'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'length': { name: 'length'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'list': { name: 'list'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'nullable': { name: 'nullable'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'options': { name: 'options'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'StringFieldOption'; ofType: null; }; }; } }; 'pattern': { name: 'pattern'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'readonly': { name: 'readonly'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiresPermission': { name: 'requiresPermission'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'ui': { name: 'ui'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; };
    'StringFieldOption': { kind: 'OBJECT'; name: 'StringFieldOption'; fields: { 'label': { name: 'label'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'value': { name: 'value'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'StringListOperators': { kind: 'INPUT_OBJECT'; name: 'StringListOperators'; isOneOf: false; inputFields: [{ name: 'inList'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }]; };
    'StringOperators': { kind: 'INPUT_OBJECT'; name: 'StringOperators'; isOneOf: false; inputFields: [{ name: 'eq'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'notEq'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'contains'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'notContains'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'in'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; }; defaultValue: null }, { name: 'notIn'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; }; defaultValue: null }, { name: 'regex'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'isNull'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }]; };
    'StringStructFieldConfig': { kind: 'OBJECT'; name: 'StringStructFieldConfig'; fields: { 'description': { name: 'description'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'label': { name: 'label'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'length': { name: 'length'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; } }; 'list': { name: 'list'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'options': { name: 'options'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'StringFieldOption'; ofType: null; }; }; } }; 'pattern': { name: 'pattern'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'ui': { name: 'ui'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; };
    'StructCustomFieldConfig': { kind: 'OBJECT'; name: 'StructCustomFieldConfig'; fields: { 'description': { name: 'description'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'fields': { name: 'fields'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'UNION'; name: 'StructFieldConfig'; ofType: null; }; }; }; } }; 'internal': { name: 'internal'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'list': { name: 'list'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'nullable': { name: 'nullable'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'readonly': { name: 'readonly'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiresPermission': { name: 'requiresPermission'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'ui': { name: 'ui'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; };
    'StructField': { kind: 'INTERFACE'; name: 'StructField'; fields: { 'description': { name: 'description'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'label': { name: 'label'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'list': { name: 'list'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'ui': { name: 'ui'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; possibleTypes: 'BooleanStructFieldConfig' | 'DateTimeStructFieldConfig' | 'FloatStructFieldConfig' | 'IntStructFieldConfig' | 'StringStructFieldConfig' | 'TextStructFieldConfig'; };
    'StructFieldConfig': { kind: 'UNION'; name: 'StructFieldConfig'; fields: {}; possibleTypes: 'BooleanStructFieldConfig' | 'DateTimeStructFieldConfig' | 'FloatStructFieldConfig' | 'IntStructFieldConfig' | 'StringStructFieldConfig' | 'TextStructFieldConfig'; };
    'Success': { kind: 'OBJECT'; name: 'Success'; fields: { 'success': { name: 'success'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; }; };
    'Surcharge': { kind: 'OBJECT'; name: 'Surcharge'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'description': { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'price': { name: 'price'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'priceWithTax': { name: 'priceWithTax'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'sku': { name: 'sku'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; } }; 'taxLines': { name: 'taxLines'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'TaxLine'; ofType: null; }; }; }; } }; 'taxRate': { name: 'taxRate'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'SurchargeInput': { kind: 'INPUT_OBJECT'; name: 'SurchargeInput'; isOneOf: false; inputFields: [{ name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'sku'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'price'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; }; defaultValue: null }, { name: 'priceIncludesTax'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; }; defaultValue: null }, { name: 'taxRate'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }, { name: 'taxDescription'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'Tag': { kind: 'OBJECT'; name: 'Tag'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'value': { name: 'value'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; }; };
    'TagFilterParameter': { kind: 'INPUT_OBJECT'; name: 'TagFilterParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'value'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'TagFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'TagFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'TagList': { kind: 'OBJECT'; name: 'TagList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Tag'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'TagListOptions': { kind: 'INPUT_OBJECT'; name: 'TagListOptions'; isOneOf: false; inputFields: [{ name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'TagSortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'TagFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'TagSortParameter': { kind: 'INPUT_OBJECT'; name: 'TagSortParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'value'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'TaxCategory': { kind: 'OBJECT'; name: 'TaxCategory'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'isDefault': { name: 'isDefault'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'TaxCategoryFilterParameter': { kind: 'INPUT_OBJECT'; name: 'TaxCategoryFilterParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'isDefault'; type: { kind: 'INPUT_OBJECT'; name: 'BooleanOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'TaxCategoryFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'TaxCategoryFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'TaxCategoryList': { kind: 'OBJECT'; name: 'TaxCategoryList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'TaxCategory'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'TaxCategoryListOptions': { kind: 'INPUT_OBJECT'; name: 'TaxCategoryListOptions'; isOneOf: false; inputFields: [{ name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'TaxCategorySortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'TaxCategoryFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'TaxCategorySortParameter': { kind: 'INPUT_OBJECT'; name: 'TaxCategorySortParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'TaxLine': { kind: 'OBJECT'; name: 'TaxLine'; fields: { 'description': { name: 'description'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'taxRate': { name: 'taxRate'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; } }; }; };
    'TaxRate': { kind: 'OBJECT'; name: 'TaxRate'; fields: { 'category': { name: 'category'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'TaxCategory'; ofType: null; }; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'customerGroup': { name: 'customerGroup'; type: { kind: 'OBJECT'; name: 'CustomerGroup'; ofType: null; } }; 'enabled': { name: 'enabled'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'value': { name: 'value'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; } }; 'zone': { name: 'zone'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Zone'; ofType: null; }; } }; }; };
    'TaxRateFilterParameter': { kind: 'INPUT_OBJECT'; name: 'TaxRateFilterParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: 'enabled'; type: { kind: 'INPUT_OBJECT'; name: 'BooleanOperators'; ofType: null; }; defaultValue: null }, { name: 'value'; type: { kind: 'INPUT_OBJECT'; name: 'NumberOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'TaxRateFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'TaxRateFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'TaxRateList': { kind: 'OBJECT'; name: 'TaxRateList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'TaxRate'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'TaxRateListOptions': { kind: 'INPUT_OBJECT'; name: 'TaxRateListOptions'; isOneOf: false; inputFields: [{ name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'TaxRateSortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'TaxRateFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'TaxRateSortParameter': { kind: 'INPUT_OBJECT'; name: 'TaxRateSortParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'value'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
    'TestEligibleShippingMethodsInput': { kind: 'INPUT_OBJECT'; name: 'TestEligibleShippingMethodsInput'; isOneOf: false; inputFields: [{ name: 'shippingAddress'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'CreateAddressInput'; ofType: null; }; }; defaultValue: null }, { name: 'lines'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'TestShippingMethodOrderLineInput'; ofType: null; }; }; }; }; defaultValue: null }]; };
    'TestShippingMethodInput': { kind: 'INPUT_OBJECT'; name: 'TestShippingMethodInput'; isOneOf: false; inputFields: [{ name: 'checker'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ConfigurableOperationInput'; ofType: null; }; }; defaultValue: null }, { name: 'calculator'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ConfigurableOperationInput'; ofType: null; }; }; defaultValue: null }, { name: 'shippingAddress'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'CreateAddressInput'; ofType: null; }; }; defaultValue: null }, { name: 'lines'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'TestShippingMethodOrderLineInput'; ofType: null; }; }; }; }; defaultValue: null }]; };
    'TestShippingMethodOrderLineInput': { kind: 'INPUT_OBJECT'; name: 'TestShippingMethodOrderLineInput'; isOneOf: false; inputFields: [{ name: 'productVariantId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'quantity'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; }; defaultValue: null }]; };
    'TestShippingMethodQuote': { kind: 'OBJECT'; name: 'TestShippingMethodQuote'; fields: { 'metadata': { name: 'metadata'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'price': { name: 'price'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; 'priceWithTax': { name: 'priceWithTax'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; } }; }; };
    'TestShippingMethodResult': { kind: 'OBJECT'; name: 'TestShippingMethodResult'; fields: { 'eligible': { name: 'eligible'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'quote': { name: 'quote'; type: { kind: 'OBJECT'; name: 'TestShippingMethodQuote'; ofType: null; } }; }; };
    'TextCustomFieldConfig': { kind: 'OBJECT'; name: 'TextCustomFieldConfig'; fields: { 'description': { name: 'description'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'internal': { name: 'internal'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'label': { name: 'label'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'list': { name: 'list'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'nullable': { name: 'nullable'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'readonly': { name: 'readonly'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; } }; 'requiresPermission': { name: 'requiresPermission'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'ui': { name: 'ui'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; };
    'TextStructFieldConfig': { kind: 'OBJECT'; name: 'TextStructFieldConfig'; fields: { 'description': { name: 'description'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'label': { name: 'label'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'LocalizedString'; ofType: null; }; }; } }; 'list': { name: 'list'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'type': { name: 'type'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'ui': { name: 'ui'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; }; };
    'TransitionFulfillmentToStateResult': { kind: 'UNION'; name: 'TransitionFulfillmentToStateResult'; fields: {}; possibleTypes: 'Fulfillment' | 'FulfillmentStateTransitionError'; };
    'TransitionOrderToStateResult': { kind: 'UNION'; name: 'TransitionOrderToStateResult'; fields: {}; possibleTypes: 'Order' | 'OrderStateTransitionError'; };
    'TransitionPaymentToStateResult': { kind: 'UNION'; name: 'TransitionPaymentToStateResult'; fields: {}; possibleTypes: 'Payment' | 'PaymentStateTransitionError'; };
    'UpdateActiveAdministratorInput': { kind: 'INPUT_OBJECT'; name: 'UpdateActiveAdministratorInput'; isOneOf: false; inputFields: [{ name: 'firstName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'lastName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'emailAddress'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'password'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateAddressInput': { kind: 'INPUT_OBJECT'; name: 'UpdateAddressInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'fullName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'company'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'streetLine1'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'streetLine2'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'city'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'province'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'postalCode'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'countryCode'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'phoneNumber'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'defaultShippingAddress'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'defaultBillingAddress'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateAdministratorInput': { kind: 'INPUT_OBJECT'; name: 'UpdateAdministratorInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'firstName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'lastName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'emailAddress'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'password'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'roleIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateAssetInput': { kind: 'INPUT_OBJECT'; name: 'UpdateAssetInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'focalPoint'; type: { kind: 'INPUT_OBJECT'; name: 'CoordinateInput'; ofType: null; }; defaultValue: null }, { name: 'tags'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateChannelInput': { kind: 'INPUT_OBJECT'; name: 'UpdateChannelInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'code'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'token'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'defaultLanguageCode'; type: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; defaultValue: null }, { name: 'availableLanguageCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; }; }; defaultValue: null }, { name: 'pricesIncludeTax'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'currencyCode'; type: { kind: 'ENUM'; name: 'CurrencyCode'; ofType: null; }; defaultValue: null }, { name: 'defaultCurrencyCode'; type: { kind: 'ENUM'; name: 'CurrencyCode'; ofType: null; }; defaultValue: null }, { name: 'availableCurrencyCodes'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'CurrencyCode'; ofType: null; }; }; }; defaultValue: null }, { name: 'trackInventory'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'outOfStockThreshold'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'defaultTaxZoneId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'defaultShippingZoneId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'sellerId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateChannelResult': { kind: 'UNION'; name: 'UpdateChannelResult'; fields: {}; possibleTypes: 'Channel' | 'LanguageNotAvailableError'; };
    'UpdateCollectionInput': { kind: 'INPUT_OBJECT'; name: 'UpdateCollectionInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'isPrivate'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'featuredAssetId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'parentId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'assetIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; defaultValue: null }, { name: 'inheritFilters'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'filters'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ConfigurableOperationInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'translations'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'UpdateCollectionTranslationInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateCollectionTranslationInput': { kind: 'INPUT_OBJECT'; name: 'UpdateCollectionTranslationInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'languageCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'slug'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateCountryInput': { kind: 'INPUT_OBJECT'; name: 'UpdateCountryInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'code'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'translations'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'CountryTranslationInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'enabled'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateCustomerGroupInput': { kind: 'INPUT_OBJECT'; name: 'UpdateCustomerGroupInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateCustomerInput': { kind: 'INPUT_OBJECT'; name: 'UpdateCustomerInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'title'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'firstName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'lastName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'phoneNumber'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'emailAddress'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateCustomerNoteInput': { kind: 'INPUT_OBJECT'; name: 'UpdateCustomerNoteInput'; isOneOf: false; inputFields: [{ name: 'noteId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'note'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }]; };
    'UpdateCustomerResult': { kind: 'UNION'; name: 'UpdateCustomerResult'; fields: {}; possibleTypes: 'Customer' | 'EmailAddressConflictError'; };
    'UpdateFacetInput': { kind: 'INPUT_OBJECT'; name: 'UpdateFacetInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'isPrivate'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'code'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'translations'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'FacetTranslationInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateFacetValueInput': { kind: 'INPUT_OBJECT'; name: 'UpdateFacetValueInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'code'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'translations'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'FacetValueTranslationInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateGlobalSettingsInput': { kind: 'INPUT_OBJECT'; name: 'UpdateGlobalSettingsInput'; isOneOf: false; inputFields: [{ name: 'availableLanguages'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'LanguageCode'; ofType: null; }; }; }; defaultValue: null }, { name: 'trackInventory'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'outOfStockThreshold'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateGlobalSettingsResult': { kind: 'UNION'; name: 'UpdateGlobalSettingsResult'; fields: {}; possibleTypes: 'ChannelDefaultLanguageError' | 'GlobalSettings'; };
    'UpdateOrderAddressInput': { kind: 'INPUT_OBJECT'; name: 'UpdateOrderAddressInput'; isOneOf: false; inputFields: [{ name: 'fullName'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'company'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'streetLine1'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'streetLine2'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'city'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'province'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'postalCode'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'countryCode'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'phoneNumber'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'UpdateOrderInput': { kind: 'INPUT_OBJECT'; name: 'UpdateOrderInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateOrderItemsResult': { kind: 'UNION'; name: 'UpdateOrderItemsResult'; fields: {}; possibleTypes: 'InsufficientStockError' | 'NegativeQuantityError' | 'Order' | 'OrderInterceptorError' | 'OrderLimitError' | 'OrderModificationError'; };
    'UpdateOrderNoteInput': { kind: 'INPUT_OBJECT'; name: 'UpdateOrderNoteInput'; isOneOf: false; inputFields: [{ name: 'noteId'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'note'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'isPublic'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }]; };
    'UpdatePaymentMethodInput': { kind: 'INPUT_OBJECT'; name: 'UpdatePaymentMethodInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'code'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'enabled'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'checker'; type: { kind: 'INPUT_OBJECT'; name: 'ConfigurableOperationInput'; ofType: null; }; defaultValue: null }, { name: 'handler'; type: { kind: 'INPUT_OBJECT'; name: 'ConfigurableOperationInput'; ofType: null; }; defaultValue: null }, { name: 'translations'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'PaymentMethodTranslationInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateProductInput': { kind: 'INPUT_OBJECT'; name: 'UpdateProductInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'enabled'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'featuredAssetId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'assetIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; defaultValue: null }, { name: 'facetValueIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; defaultValue: null }, { name: 'translations'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ProductTranslationInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateProductOptionGroupInput': { kind: 'INPUT_OBJECT'; name: 'UpdateProductOptionGroupInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'code'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'translations'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ProductOptionGroupTranslationInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateProductOptionInput': { kind: 'INPUT_OBJECT'; name: 'UpdateProductOptionInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'code'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'translations'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ProductOptionGroupTranslationInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateProductVariantInput': { kind: 'INPUT_OBJECT'; name: 'UpdateProductVariantInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'enabled'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'translations'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ProductVariantTranslationInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'facetValueIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; defaultValue: null }, { name: 'optionIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; defaultValue: null }, { name: 'sku'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'taxCategoryId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'price'; type: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; defaultValue: null }, { name: 'prices'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'UpdateProductVariantPriceInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'featuredAssetId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'assetIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; defaultValue: null }, { name: 'stockOnHand'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'stockLevels'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'StockLevelInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'outOfStockThreshold'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'useGlobalOutOfStockThreshold'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'trackInventory'; type: { kind: 'ENUM'; name: 'GlobalFlag'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateProductVariantPriceInput': { kind: 'INPUT_OBJECT'; name: 'UpdateProductVariantPriceInput'; isOneOf: false; inputFields: [{ name: 'currencyCode'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'CurrencyCode'; ofType: null; }; }; defaultValue: null }, { name: 'price'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Money'; ofType: null; }; }; defaultValue: null }, { name: 'delete'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdatePromotionInput': { kind: 'INPUT_OBJECT'; name: 'UpdatePromotionInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'enabled'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'startsAt'; type: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; defaultValue: null }, { name: 'endsAt'; type: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; defaultValue: null }, { name: 'couponCode'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'perCustomerUsageLimit'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'usageLimit'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'conditions'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ConfigurableOperationInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'actions'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ConfigurableOperationInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'translations'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'PromotionTranslationInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdatePromotionResult': { kind: 'UNION'; name: 'UpdatePromotionResult'; fields: {}; possibleTypes: 'MissingConditionsError' | 'Promotion'; };
    'UpdateProvinceInput': { kind: 'INPUT_OBJECT'; name: 'UpdateProvinceInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'code'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'translations'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ProvinceTranslationInput'; ofType: null; }; }; }; defaultValue: null }, { name: 'enabled'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateRoleInput': { kind: 'INPUT_OBJECT'; name: 'UpdateRoleInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'code'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'permissions'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'ENUM'; name: 'Permission'; ofType: null; }; }; }; defaultValue: null }, { name: 'channelIds'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; }; defaultValue: null }]; };
    'UpdateScheduledTaskInput': { kind: 'INPUT_OBJECT'; name: 'UpdateScheduledTaskInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; }; defaultValue: null }, { name: 'enabled'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }]; };
    'UpdateSellerInput': { kind: 'INPUT_OBJECT'; name: 'UpdateSellerInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateShippingMethodInput': { kind: 'INPUT_OBJECT'; name: 'UpdateShippingMethodInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'code'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'fulfillmentHandler'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'checker'; type: { kind: 'INPUT_OBJECT'; name: 'ConfigurableOperationInput'; ofType: null; }; defaultValue: null }, { name: 'calculator'; type: { kind: 'INPUT_OBJECT'; name: 'ConfigurableOperationInput'; ofType: null; }; defaultValue: null }, { name: 'translations'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ShippingMethodTranslationInput'; ofType: null; }; }; }; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateStockLocationInput': { kind: 'INPUT_OBJECT'; name: 'UpdateStockLocationInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'description'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateTagInput': { kind: 'INPUT_OBJECT'; name: 'UpdateTagInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'value'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }]; };
    'UpdateTaxCategoryInput': { kind: 'INPUT_OBJECT'; name: 'UpdateTaxCategoryInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'isDefault'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateTaxRateInput': { kind: 'INPUT_OBJECT'; name: 'UpdateTaxRateInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'value'; type: { kind: 'SCALAR'; name: 'Float'; ofType: null; }; defaultValue: null }, { name: 'enabled'; type: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; defaultValue: null }, { name: 'categoryId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'zoneId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'customerGroupId'; type: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'UpdateZoneInput': { kind: 'INPUT_OBJECT'; name: 'UpdateZoneInput'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; }; defaultValue: null }, { name: 'name'; type: { kind: 'SCALAR'; name: 'String'; ofType: null; }; defaultValue: null }, { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; }; defaultValue: null }]; };
    'Upload': unknown;
    'User': { kind: 'OBJECT'; name: 'User'; fields: { 'authenticationMethods': { name: 'authenticationMethods'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'AuthenticationMethod'; ofType: null; }; }; }; } }; 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'identifier': { name: 'identifier'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'lastLogin': { name: 'lastLogin'; type: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; } }; 'roles': { name: 'roles'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Role'; ofType: null; }; }; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'verified': { name: 'verified'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Boolean'; ofType: null; }; } }; }; };
    'Zone': { kind: 'OBJECT'; name: 'Zone'; fields: { 'createdAt': { name: 'createdAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; 'customFields': { name: 'customFields'; type: { kind: 'SCALAR'; name: 'JSON'; ofType: null; } }; 'id': { name: 'id'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'ID'; ofType: null; }; } }; 'members': { name: 'members'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INTERFACE'; name: 'Region'; ofType: null; }; }; }; } }; 'name': { name: 'name'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'String'; ofType: null; }; } }; 'updatedAt': { name: 'updatedAt'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'DateTime'; ofType: null; }; } }; }; };
    'ZoneFilterParameter': { kind: 'INPUT_OBJECT'; name: 'ZoneFilterParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'INPUT_OBJECT'; name: 'IDOperators'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'INPUT_OBJECT'; name: 'DateOperators'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'INPUT_OBJECT'; name: 'StringOperators'; ofType: null; }; defaultValue: null }, { name: '_and'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ZoneFilterParameter'; ofType: null; }; }; }; defaultValue: null }, { name: '_or'; type: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'INPUT_OBJECT'; name: 'ZoneFilterParameter'; ofType: null; }; }; }; defaultValue: null }]; };
    'ZoneList': { kind: 'OBJECT'; name: 'ZoneList'; fields: { 'items': { name: 'items'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'LIST'; name: never; ofType: { kind: 'NON_NULL'; name: never; ofType: { kind: 'OBJECT'; name: 'Zone'; ofType: null; }; }; }; } }; 'totalItems': { name: 'totalItems'; type: { kind: 'NON_NULL'; name: never; ofType: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; } }; }; };
    'ZoneListOptions': { kind: 'INPUT_OBJECT'; name: 'ZoneListOptions'; isOneOf: false; inputFields: [{ name: 'skip'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'take'; type: { kind: 'SCALAR'; name: 'Int'; ofType: null; }; defaultValue: null }, { name: 'sort'; type: { kind: 'INPUT_OBJECT'; name: 'ZoneSortParameter'; ofType: null; }; defaultValue: null }, { name: 'filter'; type: { kind: 'INPUT_OBJECT'; name: 'ZoneFilterParameter'; ofType: null; }; defaultValue: null }, { name: 'filterOperator'; type: { kind: 'ENUM'; name: 'LogicalOperator'; ofType: null; }; defaultValue: null }]; };
    'ZoneSortParameter': { kind: 'INPUT_OBJECT'; name: 'ZoneSortParameter'; isOneOf: false; inputFields: [{ name: 'id'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'createdAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'updatedAt'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }, { name: 'name'; type: { kind: 'ENUM'; name: 'SortOrder'; ofType: null; }; defaultValue: null }]; };
};

/** An IntrospectionQuery representation of your schema.
 *
 * @remarks
 * This is an introspection of your schema saved as a file by GraphQLSP.
 * It will automatically be used by `gql.tada` to infer the types of your GraphQL documents.
 * If you need to reuse this data or update your `scalars`, update `tadaOutputLocation` to
 * instead save to a .ts instead of a .d.ts file.
 */
export type introspection = {
  name: never;
  query: 'Query';
  mutation: 'Mutation';
  subscription: never;
  types: introspection_types;
};

import * as gqlTada from 'gql.tada';

declare module 'gql.tada' {
  interface setupSchema {
    introspection: introspection
  }
}