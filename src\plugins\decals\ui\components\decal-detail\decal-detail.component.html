<div class="page-block">
    <div class="action-bar">
        <div class="ab-left">
            <h1>{{ isNew ? 'Create Decal' : 'Edit Decal' }}</h1>
        </div>
        <div class="ab-right">
            <button
                class="btn btn-primary"
                type="button"
                (click)="isNew ? create() : save()"
                [disabled]="detailForm.invalid || detailForm.pristine || loading"
            >
                <span *ngIf="loading" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                {{ loading ? 'Processing...' : (isNew ? 'Create' : 'Update') }}
            </button>
        </div>
    </div>

    <form class="form" [formGroup]="detailForm" [class.loading]="loading">
        <div class="form-group">
            <label for="code">Code</label>
            <input
                id="code"
                type="text"
                class="form-control"
                formControlName="code"
                [readonly]="!isNew"
                placeholder="e.g., dragon-decal, flame-design"
                (input)="detailForm.markAsDirty()"
            />
            <small class="form-text text-muted">
                Unique identifier for this decal. Used for programmatic access and must be unique across all decals.
                {{ !isNew ? 'Cannot be changed after creation.' : 'Use lowercase letters, numbers, and hyphens only.' }}
            </small>
            <div *ngIf="detailForm.get('code')?.invalid && detailForm.get('code')?.touched" class="text-danger">
                <small *ngIf="detailForm.get('code')?.errors?.['required']">Code is required.</small>
                <small *ngIf="detailForm.get('code')?.errors?.['pattern']">Code must contain only lowercase letters, numbers, and hyphens.</small>
            </div>
        </div>

        <div class="form-group">
            <label for="name">Name</label>
            <input
                id="name"
                type="text"
                class="form-control"
                formControlName="name"
                placeholder="e.g., Dragon Decal, Flame Design"
                (input)="detailForm.markAsDirty()"
            />
            <div *ngIf="detailForm.get('name')?.invalid && detailForm.get('name')?.touched" class="text-danger">
                <small *ngIf="detailForm.get('name')?.errors?.['required']">Name is required.</small>
            </div>
        </div>

        <div class="form-group">
            <label for="description">Description</label>
            <textarea
                id="description"
                class="form-control"
                formControlName="description"
                rows="3"
                (input)="detailForm.markAsDirty()"
            ></textarea>
        </div>

        <div class="form-group">
            <label for="price">Price ($)</label>
            <input
                id="price"
                type="number"
                class="form-control"
                step="0.01"
                min="0"
                formControlName="price"
                (input)="detailForm.markAsDirty()"
            />
        </div>

        <div class="form-group">
            <label>Image</label>
            <div class="asset-picker-container">
                <div *ngIf="selectedAsset" class="selected-asset">
                    <img [src]="selectedAsset.preview" [alt]="selectedAsset.name" class="asset-preview" />
                    <div class="asset-info">
                        <span>{{ selectedAsset.name }}</span>
                        <button
                            type="button"
                            class="btn btn-sm btn-outline-secondary"
                            (click)="removeAsset()"
                        >
                            Remove
                        </button>
                    </div>
                </div>
                <div *ngIf="!selectedAsset" class="no-asset">
                    <p>No image selected</p>
                </div>
                <button
                    type="button"
                    class="btn btn-sm btn-primary"
                    (click)="selectAsset()"
                >
                    {{ selectedAsset ? 'Change Image' : 'Select Image' }}
                </button>
            </div>
        </div>

        <div class="form-group">
            <label for="enabled">Enabled</label>
            <input
                id="enabled"
                type="checkbox"
                formControlName="enabled"
                (change)="detailForm.markAsDirty()"
            />
        </div>

        <div class="color-options-section">
            <h3>Color Options</h3>
            <div formArrayName="colorOptions">
                <div
                    *ngFor="let colorOption of colorOptionsFormArray.controls; let i = index"
                    [formGroupName]="i"
                    class="color-option-row"
                >
                    <div class="form-group">
                        <label [for]="'colorName' + i">Color Name</label>
                        <input
                            [id]="'colorName' + i"
                            type="text"
                            class="form-control"
                            formControlName="name"
                            placeholder="e.g., Red, Blue"
                            (input)="detailForm.markAsDirty()"
                        />
                    </div>

                    <div class="form-group">
                        <label [for]="'hexCode' + i">Hex Code</label>
                        <input
                            [id]="'hexCode' + i"
                            type="text"
                            class="form-control"
                            formControlName="hexCode"
                            placeholder="#000000"
                            pattern="^#[0-9A-Fa-f]{6}$"
                            (input)="detailForm.markAsDirty()"
                        />
                    </div>

                    <div class="form-group">
                        <label [for]="'priceModifier' + i">Price Modifier ($)</label>
                        <input
                            [id]="'priceModifier' + i"
                            type="number"
                            class="form-control"
                            step="0.01"
                            formControlName="priceModifier"
                            placeholder="0.00"
                            (input)="detailForm.markAsDirty()"
                        />
                    </div>

                    <button
                        type="button"
                        class="btn btn-sm btn-danger"
                        (click)="removeColorOption(i)"
                    >
                        Remove
                    </button>
                </div>

                <button
                    type="button"
                    class="btn btn-sm btn-secondary"
                    (click)="addColorOption()"
                >
                    Add Color Option
                </button>
            </div>
        </div>
    </form>
</div>
