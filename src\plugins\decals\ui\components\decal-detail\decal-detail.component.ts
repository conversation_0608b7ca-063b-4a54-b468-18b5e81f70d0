import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import {
    DataService,
    NotificationService,
    SharedModule,
    ModalService,
    AssetPickerDialogComponent,
} from '@vendure/admin-ui/core';
import { gql } from 'apollo-angular';
import { firstValueFrom } from 'rxjs';

export const GET_DECAL = gql`
    query GetDecal($id: ID!) {
        decal(id: $id) {
            id
            createdAt
            updatedAt
            code
            name
            description
            price
            enabled
            image {
                id
                name
                preview
                source
            }
            colorOptions {
                name
                hexCode
                priceModifier
            }
        }
    }
`;

export const CREATE_DECAL = gql`
    mutation CreateDecal($input: CreateDecalInput!) {
        createDecal(input: $input) {
            id
            code
            name
            description
            price
            enabled
            image {
                id
                preview
            }
            colorOptions {
                name
                hexCode
                priceModifier
            }
        }
    }
`;

export const UPDATE_DECAL = gql`
    mutation UpdateDecal($input: UpdateDecalInput!) {
        updateDecal(input: $input) {
            id
            code
            name
            description
            price
            enabled
            image {
                id
                preview
            }
            colorOptions {
                name
                hexCode
                priceModifier
            }
        }
    }
`;

@Component({
    selector: 'vdr-decal-detail',
    templateUrl: './decal-detail.component.html',
    styleUrls: ['./decal-detail.component.css'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [SharedModule],
})
export class DecalDetailComponent implements OnInit {
    detailForm: FormGroup;
    isNew = true;
    decalId: string | null = null;
    loading = false;
    selectedAsset: any = null;

    constructor(
        private formBuilder: FormBuilder,
        private dataService: DataService,
        private notificationService: NotificationService,
        private router: Router,
        private route: ActivatedRoute,
        private modalService: ModalService,
    ) {
        this.detailForm = this.formBuilder.group({
            code: ['', [Validators.required, Validators.pattern(/^[a-z0-9-]+$/)]],
            name: ['', Validators.required],
            description: [''],
            price: [0, [Validators.required, Validators.min(0)]],
            enabled: [true],
            imageId: [null],
            colorOptions: this.formBuilder.array([]),
        });
    }

    ngOnInit() {
        this.route.params.subscribe(params => {
            if (params['id']) {
                this.isNew = false;
                this.decalId = params['id'];
                this.loadDecal(params['id']);
            }
        });
    }

    get colorOptionsFormArray(): FormArray {
        return this.detailForm.get('colorOptions') as FormArray;
    }

    async loadDecal(id: string) {
        try {
            this.loading = true;
            const queryResult = this.dataService.query(GET_DECAL, { id });
            const result = await firstValueFrom(queryResult.single$);

            if ((result as any).decal) {
                const decal = (result as any).decal;

                // Clear existing color options
                while (this.colorOptionsFormArray.length !== 0) {
                    this.colorOptionsFormArray.removeAt(0);
                }

                // Populate form with decal data
                this.detailForm.patchValue({
                    code: decal.code,
                    name: decal.name,
                    description: decal.description,
                    price: decal.price / 100, // Convert from cents to dollars
                    enabled: decal.enabled,
                    imageId: decal.image?.id || null,
                });

                // Set selected asset for display
                this.selectedAsset = decal.image;

                // Add color options
                if (decal.colorOptions) {
                    decal.colorOptions.forEach((option: any) => {
                        this.addColorOption({
                            name: option.name,
                            hexCode: option.hexCode,
                            priceModifier: option.priceModifier / 100, // Convert from cents to dollars
                        });
                    });
                }

                this.detailForm.markAsPristine();
            }
        } catch (error) {
            this.notificationService.error('Failed to load decal: ' + (error as Error).message);
        } finally {
            this.loading = false;
        }
    }

    async create(): Promise<void> {
        if (!this.detailForm.valid) {
            this.markFormGroupTouched(this.detailForm);
            return;
        }

        try {
            this.loading = true;
            const formValue = this.detailForm.value;

            // Prepare the input data
            const input = {
                code: formValue.code,
                name: formValue.name,
                description: formValue.description || null,
                price: Math.round(formValue.price * 100), // Convert to cents
                enabled: formValue.enabled,
                imageId: formValue.imageId || null,
                colorOptions: formValue.colorOptions.map((option: any) => ({
                    name: option.name,
                    hexCode: option.hexCode,
                    priceModifier: Math.round(option.priceModifier * 100), // Convert to cents
                })),
            };

            await firstValueFrom(
                this.dataService.mutate(CREATE_DECAL, { input })
            );

            this.notificationService.success('Decal created successfully');
            this.router.navigate(['../'], { relativeTo: this.route });
        } catch (error) {
            this.notificationService.error('Failed to create decal: ' + (error as Error).message);
        } finally {
            this.loading = false;
        }
    }

    async save(): Promise<void> {
        if (!this.detailForm.valid) {
            this.markFormGroupTouched(this.detailForm);
            return;
        }

        try {
            this.loading = true;
            const formValue = this.detailForm.value;

            // Prepare the input data
            const input = {
                id: this.decalId,
                code: formValue.code,
                name: formValue.name,
                description: formValue.description || null,
                price: Math.round(formValue.price * 100), // Convert to cents
                enabled: formValue.enabled,
                imageId: formValue.imageId || null,
                colorOptions: formValue.colorOptions.map((option: any) => ({
                    name: option.name,
                    hexCode: option.hexCode,
                    priceModifier: Math.round(option.priceModifier * 100), // Convert to cents
                })),
            };

            await firstValueFrom(
                this.dataService.mutate(UPDATE_DECAL, { input })
            );

            this.notificationService.success('Decal updated successfully');
            this.detailForm.markAsPristine();
        } catch (error) {
            this.notificationService.error('Failed to update decal: ' + (error as Error).message);
        } finally {
            this.loading = false;
        }
    }

    addColorOption(option?: any): void {
        const colorOptionGroup = this.formBuilder.group({
            name: [option?.name || '', Validators.required],
            hexCode: [option?.hexCode || '#000000', [Validators.required, Validators.pattern(/^#[0-9A-Fa-f]{6}$/)]],
            priceModifier: [option?.priceModifier ? option.priceModifier / 100 : 0, Validators.required],
        });
        this.colorOptionsFormArray.push(colorOptionGroup);
    }

    removeColorOption(index: number): void {
        this.colorOptionsFormArray.removeAt(index);
    }

    selectAsset(): void {
        this.modalService
            .fromComponent(AssetPickerDialogComponent, {
                size: 'xl',
            })
            .subscribe(result => {
                if (result && result.length) {
                    const asset = result[0];
                    this.selectedAsset = asset;
                    this.detailForm.patchValue({ imageId: asset.id });
                    this.detailForm.markAsDirty();
                }
            });
    }

    removeAsset(): void {
        this.selectedAsset = null;
        this.detailForm.patchValue({ imageId: null });
        this.detailForm.markAsDirty();
    }

    private markFormGroupTouched(formGroup: FormGroup): void {
        Object.keys(formGroup.controls).forEach(key => {
            const control = formGroup.get(key);
            control?.markAsTouched();

            if (control instanceof FormGroup) {
                this.markFormGroupTouched(control);
            }
        });
    }
}
