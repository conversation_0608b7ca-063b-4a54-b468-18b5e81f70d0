import { ID } from '@vendure/core';

/**
 * @description
 * The plugin can be configured using the following options:
 */
export interface PluginInitOptions {
    exampleOption?: string;
}

/**
 * @description
 * Represents a selected decal with chosen color for an order line
 */
export interface SelectedDecal {
    decalId: ID;
    colorOption?: {
        name: string;
        hexCode: string;
        priceModifier: number;
    };
}

/**
 * @description
 * Custom fields for OrderLine to store decal selections
 */
export interface OrderLineDecalCustomFields {
    selectedDecals?: SelectedDecal[];
}
