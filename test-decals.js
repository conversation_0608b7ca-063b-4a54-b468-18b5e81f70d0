// Simple test script to verify decal functionality
const { gql, request } = require('graphql-request');

const endpoint = 'http://localhost:3000/admin-api';

// Test query to fetch decals
const GET_DECALS = gql`
    query GetDecals {
        decals(options: {}) {
            items {
                id
                code
                name
                description
                price
                enabled
                image {
                    id
                    preview
                }
                colorOptions {
                    name
                    hexCode
                    priceModifier
                }
            }
            totalItems
        }
    }
`;

// Test mutation to create a decal
const CREATE_DECAL = gql`
    mutation CreateDecal($input: CreateDecalInput!) {
        createDecal(input: $input) {
            id
            code
            name
            description
            price
            enabled
            image {
                id
                preview
            }
            colorOptions {
                name
                hexCode
                priceModifier
            }
        }
    }
`;

async function testDecals() {
    try {
        console.log('Testing decal functionality...');
        
        // First, try to fetch existing decals
        console.log('Fetching existing decals...');
        const decalsResult = await request(endpoint, GET_DECALS);
        console.log('Existing decals:', JSON.stringify(decalsResult, null, 2));
        
        // Try to create a test decal
        console.log('Creating test decal...');
        const createInput = {
            input: {
                code: 'test-decal-' + Date.now(),
                name: 'Test Decal',
                description: 'A test decal for verification',
                price: 500, // $5.00 in cents
                enabled: true,
                colorOptions: [
                    {
                        name: 'Red',
                        hexCode: '#FF0000',
                        priceModifier: 0
                    },
                    {
                        name: 'Blue',
                        hexCode: '#0000FF',
                        priceModifier: 100 // $1.00 extra
                    }
                ]
            }
        };
        
        const createResult = await request(endpoint, CREATE_DECAL, createInput);
        console.log('Created decal:', JSON.stringify(createResult, null, 2));
        
        // Fetch decals again to verify
        console.log('Fetching decals after creation...');
        const updatedDecalsResult = await request(endpoint, GET_DECALS);
        console.log('Updated decals list:', JSON.stringify(updatedDecalsResult, null, 2));
        
    } catch (error) {
        console.error('Error testing decals:', error);
    }
}

// Run the test if this script is executed directly
if (require.main === module) {
    testDecals();
}

module.exports = { testDecals };
