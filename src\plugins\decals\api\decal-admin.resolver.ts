import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';
import { DeletionResponse } from '@vendure/common/lib/generated-types';
import { 
    Allow, 
    Ctx, 
    ID, 
    ListQueryOptions, 
    PaginatedList, 
    Permission, 
    RequestContext, 
    Transaction 
} from '@vendure/core';
import { DecalService } from '../services/decal.service';
import { Decal } from '../entities/decal.entity';

// Input types - these would normally be generated from the GraphQL schema
interface CreateDecalInput {
    code: string;
    name: string;
    description?: string;
    price: number;
    colorOptions?: Array<{
        name: string;
        hexCode: string;
        priceModifier: number;
    }>;
    imageId?: ID;
    enabled?: boolean;
}

interface UpdateDecalInput {
    id: ID;
    code?: string;
    name?: string;
    description?: string;
    price?: number;
    colorOptions?: Array<{
        name: string;
        hexCode: string;
        priceModifier: number;
    }>;
    imageId?: ID;
    enabled?: boolean;
}

interface DecalListOptions extends ListQueryOptions<Decal> {}

@Resolver()
export class DecalAdminResolver {
    constructor(private decalService: DecalService) {}

    @Query()
    @Allow(Permission.ReadCatalog)
    async decal(@Ctx() ctx: RequestContext, @Args() args: { id: ID }): Promise<Decal | null> {
        return this.decalService.findOne(ctx, args.id);
    }

    @Query()
    @Allow(Permission.ReadCatalog)
    async decals(
        @Ctx() ctx: RequestContext,
        @Args() args: { options?: DecalListOptions }
    ): Promise<PaginatedList<Decal>> {
        return this.decalService.findAll(ctx, args.options);
    }

    @Mutation()
    @Transaction()
    @Allow(Permission.CreateCatalog)
    async createDecal(
        @Ctx() ctx: RequestContext,
        @Args() args: { input: CreateDecalInput }
    ): Promise<Decal> {
        return this.decalService.create(ctx, args.input);
    }

    @Mutation()
    @Transaction()
    @Allow(Permission.UpdateCatalog)
    async updateDecal(
        @Ctx() ctx: RequestContext,
        @Args() args: { input: UpdateDecalInput }
    ): Promise<Decal> {
        return this.decalService.update(ctx, args.input);
    }

    @Mutation()
    @Transaction()
    @Allow(Permission.DeleteCatalog)
    async deleteDecal(
        @Ctx() ctx: RequestContext,
        @Args() args: { id: ID }
    ): Promise<DeletionResponse> {
        return this.decalService.delete(ctx, args.id);
    }

    @Mutation()
    @Transaction()
    @Allow(Permission.DeleteCatalog)
    async deleteDecals(
        @Ctx() ctx: RequestContext,
        @Args() args: { ids: ID[] }
    ): Promise<DeletionResponse[]> {
        const results: DeletionResponse[] = [];
        for (const id of args.ids) {
            results.push(await this.decalService.delete(ctx, id));
        }
        return results;
    }
}
