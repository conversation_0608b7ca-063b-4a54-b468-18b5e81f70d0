{{> header title="Order Receipt" }}

<mj-raw>
    <style type="text/css">
    .callout {
        background-color: #375a67;
        padding: 15px 0;
    }
    .callout-large > div {
        text-align: center !important;
        color: #fff !important;
        font-size: 16px !important;
        font-weight: bold;
        padding: 0;
    }
    .callout-small > div {
        text-align: center !important;
        color: #fff !important;
        font-size: 14px !important;
        padding: 0;
    }
    ul.address {
        list-style-type: none;
        padding: 0;
    }
    tr.order-row td {
        border-bottom: 1px dashed #eee;
    }
    tr.order-row td:last-child {
        text-align: center;
    }
    tr.total-row {
        font-weight: bold;
    }
    .bg-off-white {
        background-color: #f5f5f5;
    }
    </style>
</mj-raw>

<mj-section css-class="bg-off-white">
    <mj-column>
        <mj-text>
            Dear {{ order.customer.firstName }} {{ order.customer.lastName }},
        </mj-text>
        <mj-text>
            Thank you for your order!
        </mj-text>
    </mj-column>
</mj-section>


<mj-section css-class="callout">
    <mj-column>
        <mj-text css-class="callout-large"><strong>Order Code</strong></mj-text>
        <mj-text css-class="callout-small">{{ order.code }}</mj-text>
    </mj-column>
    <mj-column>
        <mj-text css-class="callout-large"><strong>Order Date</strong></mj-text>
        <mj-text css-class="callout-small">{{ formatDate order.orderPlacedAt }}</mj-text>
    </mj-column>
    <mj-column>
        <mj-text css-class="callout-large"><strong>Total Price</strong></mj-text>
        <mj-text css-class="callout-small">{{ formatMoney order.total order.currencyCode 'en' }}</mj-text>
    </mj-column>
</mj-section>


<mj-section css-class="bg-off-white">
    <mj-column>
        <mj-text>
            {{#with order.shippingAddress }}
                <h3>Shipping To: {{ fullName }}</h3>
                <ul class="address">
                    {{#if company}}<li>{{ company }}</li>{{/if}}
                    {{#if streetLine1}}<li>{{ streetLine1 }}</li>{{/if}}
                    {{#if streetLine2}}<li>{{ streetLine2 }}</li>{{/if}}
                    {{#if city}}<li>{{ city }}</li>{{/if}}
                    {{#if province}}<li>{{ province }}</li>{{/if}}
                    {{#if postalCode}}<li>{{ postalCode }}</li>{{/if}}
                    {{#if country}}<li>{{ country }}</li>{{/if}}
                    {{#if phoneNumber}}<li>{{ phoneNumber }}</li>{{/if}}
                </ul>
            {{/with}}
        </mj-text>
    </mj-column>
</mj-section>

<mj-section>
    <mj-column>
        <mj-text>
            <h3>Order Summary:</h3>
        </mj-text>
        <mj-table cellpadding="6px">
            {{#each order.lines }}
                <tr class="order-row">
                    <td>
                        <img alt="{{ productVariant.name }}"
                             style="width: 50px; height: 50px;"
                             src="{{ featuredAsset.preview }}?w=50&h=50" />
                    </td>
                    <td>{{ quantity }} x {{ productVariant.name }}</td>
                    <td>{{ productVariant.quantity }}</td>
                    <td>{{ formatMoney discountedLinePriceWithTax ../order.currencyCode 'en' }}</td>
                </tr>
            {{/each}}
            {{#each order.discounts }}
                <tr class="order-row">
                    <td colspan="3">
                        {{ description }}
                    </td>
                    <td>{{ formatMoney amount ../order.currencyCode 'en' }}</td>
                </tr>
            {{/each}}
            <tr class="order-row">
                <td colspan="3">Sub-total:</td>
                <td>{{ formatMoney order.subTotalWithTax order.currencyCode 'en' }}</td>
            </tr>
            {{#each shippingLines }}
            <tr class="order-row">
                <td colspan="3">Shipping ({{ shippingMethod.name }}):</td>
                <td>{{ formatMoney priceWithTax ../order.currencyCode 'en' }}</td>
            </tr>
            {{/each}}
            <tr class="order-row total-row">
                <td colspan="3">Total:</td>
                <td>{{ formatMoney order.totalWithTax order.currencyCode 'en' }}</td>
            </tr>
        </mj-table>
    </mj-column>
</mj-section>

{{> footer }}
