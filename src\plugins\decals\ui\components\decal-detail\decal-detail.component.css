.asset-picker-container {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    background-color: #f9f9f9;
}

.selected-asset {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 10px;
}

.asset-preview {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.asset-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.asset-info span {
    font-weight: 500;
    color: #333;
}

.no-asset {
    margin-bottom: 10px;
}

.no-asset p {
    color: #666;
    font-style: italic;
    margin: 0;
}

.color-options-section {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.color-option-row {
    display: flex;
    gap: 15px;
    align-items: end;
    margin-bottom: 15px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.color-option-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

.color-option-row button {
    flex-shrink: 0;
}
