import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import {
    DataService,
    ModalService,
    NotificationService,
    SharedModule,
} from '@vendure/admin-ui/core';
import { gql } from 'apollo-angular';
import { firstValueFrom } from 'rxjs';

const GET_DECAL_LIST = gql`
    query GetDecalList($options: DecalListOptions) {
        decals(options: $options) {
            items {
                id
                createdAt
                updatedAt
                code
                name
                description
                price
                enabled
                image {
                    id
                    preview
                }
                colorOptions {
                    name
                    hexCode
                    priceModifier
                }
            }
            totalItems
        }
    }
`;

const DELETE_DECAL = gql`
    mutation DeleteDecal($id: ID!) {
        deleteDecal(id: $id) {
            result
            message
        }
    }
`;

const DELETE_DECALS = gql`
    mutation DeleteDecals($ids: [ID!]!) {
        deleteDecals(ids: $ids) {
            result
            message
        }
    }
`;

interface DecalListItem {
    id: string;
    createdAt: string;
    updatedAt: string;
    code: string;
    name: string;
    description?: string;
    price: number;
    enabled: boolean;
    image?: {
        id: string;
        preview: string;
    };
    colorOptions: Array<{
        name: string;
        hexCode: string;
        priceModifier: number;
    }>;
}

@Component({
    selector: 'vdr-decal-list',
    templateUrl: './decal-list.component.html',
    standalone: true,
    imports: [SharedModule],
})
export class DecalListComponent implements OnInit {
    decals: DecalListItem[] = [];
    loading = false;

    constructor(
        private dataService: DataService,
        private router: Router,
        private route: ActivatedRoute,
        private modalService: ModalService,
        private notificationService: NotificationService,
    ) {}

    ngOnInit() {
        this.loadDecals();
    }

    loadDecals() {
        this.loading = true;
        this.dataService.query(GET_DECAL_LIST, { options: {} }).single$.subscribe({
            next: (result: any) => {
                this.decals = result.decals?.items || [];
                this.loading = false;
            },
            error: (error) => {
                console.error('Failed to load decals:', error);
                this.notificationService.error('Failed to load decals: ' + error.message);
                this.decals = [];
                this.loading = false;
            }
        });
    }

    create() {
        this.router.navigate(['./create'], { relativeTo: this.route });
    }

    async deleteDecal(decal: DecalListItem) {
        const confirmed = confirm('Are you sure you want to delete this decal?');
        if (confirmed) {
            try {
                await firstValueFrom(this.dataService.mutate(DELETE_DECAL, { id: decal.id }));
                this.notificationService.success('Decal deleted successfully');
                this.loadDecals();
            } catch (error) {
                console.error('Failed to delete decal:', error);
                this.notificationService.error('Failed to delete decal: ' + (error as Error).message);
            }
        }
    }

    formatPrice(price: number): string {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(price / 100);
    }
}
