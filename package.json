{"name": "sugarjays-vendure", "version": "0.1.0", "private": true, "scripts": {"dev:server": "ts-node ./src/index.ts", "dev:worker": "ts-node ./src/index-worker.ts", "dev": "concurrently npm:dev:*", "build": "tsc", "start:server": "node ./dist/index.js", "start:worker": "node ./dist/index-worker.js", "start": "concurrently npm:start:*"}, "dependencies": {"@vendure/admin-ui-plugin": "3.3.2", "@vendure/asset-server-plugin": "3.3.2", "@vendure/core": "3.3.2", "@vendure/dashboard": "^3.3.2", "@vendure/email-plugin": "3.3.2", "@vendure/graphiql-plugin": "3.3.2", "better-sqlite3": "11.10.0", "dotenv": "16.5.0"}, "devDependencies": {"@types/react": "19.1.6", "@vendure/cli": "3.3.2", "@vendure/ui-devkit": "3.3.2", "concurrently": "9.1.2", "typescript": "5.8.2"}}