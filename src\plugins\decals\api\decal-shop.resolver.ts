import { Args, Query, Resolver } from '@nestjs/graphql';
import { Ctx, ID, RequestContext } from '@vendure/core';
import { DecalService } from '../services/decal.service';
import { Decal } from '../entities/decal.entity';

@Resolver()
export class DecalShopResolver {
    constructor(private decalService: DecalService) {}

    @Query()
    async availableDecals(@Ctx() ctx: RequestContext): Promise<Decal[]> {
        return this.decalService.findEnabledDecals(ctx);
    }

    @Query()
    async decal(@Ctx() ctx: RequestContext, @Args() args: { id: ID }): Promise<Decal | null> {
        const decal = await this.decalService.findOne(ctx, args.id);
        // Only return enabled decals in the shop API
        return decal && decal.enabled ? decal : null;
    }
}
