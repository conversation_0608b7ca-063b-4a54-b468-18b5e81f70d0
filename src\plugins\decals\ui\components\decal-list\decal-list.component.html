<div class="page-block">
    <div class="action-bar">
        <div class="ab-left">
            <h1>Decals</h1>
        </div>
        <div class="ab-right">
            <button
                class="btn btn-primary"
                type="button"
                (click)="create()"
            >
                Create New Decal
            </button>
        </div>
    </div>

    <div class="content" *ngIf="!loading">
        <div *ngIf="decals.length === 0" class="empty-state">
            <p>No decals found. Create your first decal to get started.</p>
        </div>

        <div *ngIf="decals.length > 0" class="decal-grid">
            <div *ngFor="let decal of decals" class="decal-card">
                <div class="decal-info">
                    <h3>{{ decal.name }}</h3>
                    <p>{{ decal.code }}</p>
                    <p>{{ decal.description }}</p>
                    <p>{{ formatPrice(decal.price) }}</p>
                </div>
                <div class="decal-actions">
                    <button
                        class="btn btn-sm btn-secondary"
                        [routerLink]="['./', decal.id]"
                    >
                        Edit
                    </button>
                    <button
                        class="btn btn-sm btn-danger"
                        (click)="deleteDecal(decal)"
                    >
                        Delete
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div *ngIf="loading" class="loading">
        <p>Loading decals...</p>
    </div>
</div>
