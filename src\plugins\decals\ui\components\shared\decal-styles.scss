// Decal List Styles
.decal-image {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
}

.image-placeholder {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
}

.description-text {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.price {
    font-weight: 600;
    color: #2e7d32;
}

.color-options {
    display: flex;
    gap: 4px;
    align-items: center;
}

.color-chip {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
    cursor: help;
}

// Decal Detail Styles
.color-option-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr auto;
    gap: 16px;
    align-items: end;
    padding: 16px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    margin-bottom: 16px;
    background-color: #fafafa;
}

.hex-input-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.color-preview {
    width: 30px;
    height: 30px;
    border-radius: 4px;
    border: 2px solid #fff;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.input-group {
    display: flex;
    align-items: center;
}

.input-group-text {
    background-color: #f5f5f5;
    border: 1px solid #ccc;
    border-right: none;
    padding: 8px 12px;
    border-radius: 4px 0 0 4px;
    font-weight: 500;
}

.input-group input {
    border-radius: 0 4px 4px 0;
    border-left: none;
}

// Form validation styles
.form-field.ng-invalid.ng-touched input,
.form-field.ng-invalid.ng-touched textarea {
    border-color: #d32f2f;
}

.form-field.ng-invalid.ng-touched .error-message {
    color: #d32f2f;
    font-size: 12px;
    margin-top: 4px;
}

// Responsive adjustments
@media (max-width: 768px) {
    .color-option-row {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .description-text {
        max-width: 150px;
    }
}
