import {MigrationInterface, QueryRunner} from "typeorm";

export class DecalNew1748995153200 implements MigrationInterface {

   public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`CREATE TABLE "decal" ("createdAt" datetime NOT NULL DEFAULT (datetime('now')), "updatedAt" datetime NOT NULL DEFAULT (datetime('now')), "code" varchar NOT NULL, "name" varchar NOT NULL, "description" text, "price" integer NOT NULL, "colorOptions" text, "enabled" boolean NOT NULL DEFAULT (1), "id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "imageId" integer)`, undefined);
        await queryRunner.query(`DROP INDEX "IDX_239cfca2a55b98b90b6bef2e44"`, undefined);
        await queryRunner.query(`DROP INDEX "IDX_9f065453910ea77d4be8e92618"`, undefined);
        await queryRunner.query(`DROP INDEX "IDX_77be94ce9ec650446617946227"`, undefined);
        await queryRunner.query(`DROP INDEX "IDX_cbcd22193eda94668e84d33f18"`, undefined);
        await queryRunner.query(`DROP INDEX "IDX_dc9ac68b47da7b62249886affb"`, undefined);
        await queryRunner.query(`DROP INDEX "IDX_6901d8715f5ebadd764466f7bd"`, undefined);
        await queryRunner.query(`CREATE TABLE "temporary_order_line" ("createdAt" datetime NOT NULL DEFAULT (datetime('now')), "updatedAt" datetime NOT NULL DEFAULT (datetime('now')), "quantity" integer NOT NULL, "orderPlacedQuantity" integer NOT NULL DEFAULT (0), "listPriceIncludesTax" boolean NOT NULL, "adjustments" text NOT NULL, "taxLines" text NOT NULL, "id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "sellerChannelId" integer, "shippingLineId" integer, "productVariantId" integer NOT NULL, "taxCategoryId" integer, "initialListPrice" integer, "listPrice" integer NOT NULL, "featuredAssetId" integer, "orderId" integer, "customFieldsSelecteddecals" varchar(255), CONSTRAINT "FK_239cfca2a55b98b90b6bef2e44f" FOREIGN KEY ("orderId") REFERENCES "order" ("id") ON DELETE CASCADE ON UPDATE NO ACTION, CONSTRAINT "FK_9f065453910ea77d4be8e92618f" FOREIGN KEY ("featuredAssetId") REFERENCES "asset" ("id") ON DELETE SET NULL ON UPDATE NO ACTION, CONSTRAINT "FK_77be94ce9ec6504466179462275" FOREIGN KEY ("taxCategoryId") REFERENCES "tax_category" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION, CONSTRAINT "FK_cbcd22193eda94668e84d33f185" FOREIGN KEY ("productVariantId") REFERENCES "product_variant" ("id") ON DELETE CASCADE ON UPDATE NO ACTION, CONSTRAINT "FK_dc9ac68b47da7b62249886affba" FOREIGN KEY ("shippingLineId") REFERENCES "shipping_line" ("id") ON DELETE SET NULL ON UPDATE NO ACTION, CONSTRAINT "FK_6901d8715f5ebadd764466f7bde" FOREIGN KEY ("sellerChannelId") REFERENCES "channel" ("id") ON DELETE SET NULL ON UPDATE NO ACTION)`, undefined);
        await queryRunner.query(`INSERT INTO "temporary_order_line"("createdAt", "updatedAt", "quantity", "orderPlacedQuantity", "listPriceIncludesTax", "adjustments", "taxLines", "id", "sellerChannelId", "shippingLineId", "productVariantId", "taxCategoryId", "initialListPrice", "listPrice", "featuredAssetId", "orderId") SELECT "createdAt", "updatedAt", "quantity", "orderPlacedQuantity", "listPriceIncludesTax", "adjustments", "taxLines", "id", "sellerChannelId", "shippingLineId", "productVariantId", "taxCategoryId", "initialListPrice", "listPrice", "featuredAssetId", "orderId" FROM "order_line"`, undefined);
        await queryRunner.query(`DROP TABLE "order_line"`, undefined);
        await queryRunner.query(`ALTER TABLE "temporary_order_line" RENAME TO "order_line"`, undefined);
        await queryRunner.query(`CREATE INDEX "IDX_239cfca2a55b98b90b6bef2e44" ON "order_line" ("orderId") `, undefined);
        await queryRunner.query(`CREATE INDEX "IDX_9f065453910ea77d4be8e92618" ON "order_line" ("featuredAssetId") `, undefined);
        await queryRunner.query(`CREATE INDEX "IDX_77be94ce9ec650446617946227" ON "order_line" ("taxCategoryId") `, undefined);
        await queryRunner.query(`CREATE INDEX "IDX_cbcd22193eda94668e84d33f18" ON "order_line" ("productVariantId") `, undefined);
        await queryRunner.query(`CREATE INDEX "IDX_dc9ac68b47da7b62249886affb" ON "order_line" ("shippingLineId") `, undefined);
        await queryRunner.query(`CREATE INDEX "IDX_6901d8715f5ebadd764466f7bd" ON "order_line" ("sellerChannelId") `, undefined);
        await queryRunner.query(`CREATE TABLE "temporary_decal" ("createdAt" datetime NOT NULL DEFAULT (datetime('now')), "updatedAt" datetime NOT NULL DEFAULT (datetime('now')), "code" varchar NOT NULL, "name" varchar NOT NULL, "description" text, "price" integer NOT NULL, "colorOptions" text, "enabled" boolean NOT NULL DEFAULT (1), "id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "imageId" integer, CONSTRAINT "FK_5c7763a98c2d596cc036ee98db4" FOREIGN KEY ("imageId") REFERENCES "asset" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION)`, undefined);
        await queryRunner.query(`INSERT INTO "temporary_decal"("createdAt", "updatedAt", "code", "name", "description", "price", "colorOptions", "enabled", "id", "imageId") SELECT "createdAt", "updatedAt", "code", "name", "description", "price", "colorOptions", "enabled", "id", "imageId" FROM "decal"`, undefined);
        await queryRunner.query(`DROP TABLE "decal"`, undefined);
        await queryRunner.query(`ALTER TABLE "temporary_decal" RENAME TO "decal"`, undefined);
   }

   public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "decal" RENAME TO "temporary_decal"`, undefined);
        await queryRunner.query(`CREATE TABLE "decal" ("createdAt" datetime NOT NULL DEFAULT (datetime('now')), "updatedAt" datetime NOT NULL DEFAULT (datetime('now')), "code" varchar NOT NULL, "name" varchar NOT NULL, "description" text, "price" integer NOT NULL, "colorOptions" text, "enabled" boolean NOT NULL DEFAULT (1), "id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "imageId" integer)`, undefined);
        await queryRunner.query(`INSERT INTO "decal"("createdAt", "updatedAt", "code", "name", "description", "price", "colorOptions", "enabled", "id", "imageId") SELECT "createdAt", "updatedAt", "code", "name", "description", "price", "colorOptions", "enabled", "id", "imageId" FROM "temporary_decal"`, undefined);
        await queryRunner.query(`DROP TABLE "temporary_decal"`, undefined);
        await queryRunner.query(`DROP INDEX "IDX_6901d8715f5ebadd764466f7bd"`, undefined);
        await queryRunner.query(`DROP INDEX "IDX_dc9ac68b47da7b62249886affb"`, undefined);
        await queryRunner.query(`DROP INDEX "IDX_cbcd22193eda94668e84d33f18"`, undefined);
        await queryRunner.query(`DROP INDEX "IDX_77be94ce9ec650446617946227"`, undefined);
        await queryRunner.query(`DROP INDEX "IDX_9f065453910ea77d4be8e92618"`, undefined);
        await queryRunner.query(`DROP INDEX "IDX_239cfca2a55b98b90b6bef2e44"`, undefined);
        await queryRunner.query(`ALTER TABLE "order_line" RENAME TO "temporary_order_line"`, undefined);
        await queryRunner.query(`CREATE TABLE "order_line" ("createdAt" datetime NOT NULL DEFAULT (datetime('now')), "updatedAt" datetime NOT NULL DEFAULT (datetime('now')), "quantity" integer NOT NULL, "orderPlacedQuantity" integer NOT NULL DEFAULT (0), "listPriceIncludesTax" boolean NOT NULL, "adjustments" text NOT NULL, "taxLines" text NOT NULL, "id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "sellerChannelId" integer, "shippingLineId" integer, "productVariantId" integer NOT NULL, "taxCategoryId" integer, "initialListPrice" integer, "listPrice" integer NOT NULL, "featuredAssetId" integer, "orderId" integer, CONSTRAINT "FK_239cfca2a55b98b90b6bef2e44f" FOREIGN KEY ("orderId") REFERENCES "order" ("id") ON DELETE CASCADE ON UPDATE NO ACTION, CONSTRAINT "FK_9f065453910ea77d4be8e92618f" FOREIGN KEY ("featuredAssetId") REFERENCES "asset" ("id") ON DELETE SET NULL ON UPDATE NO ACTION, CONSTRAINT "FK_77be94ce9ec6504466179462275" FOREIGN KEY ("taxCategoryId") REFERENCES "tax_category" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION, CONSTRAINT "FK_cbcd22193eda94668e84d33f185" FOREIGN KEY ("productVariantId") REFERENCES "product_variant" ("id") ON DELETE CASCADE ON UPDATE NO ACTION, CONSTRAINT "FK_dc9ac68b47da7b62249886affba" FOREIGN KEY ("shippingLineId") REFERENCES "shipping_line" ("id") ON DELETE SET NULL ON UPDATE NO ACTION, CONSTRAINT "FK_6901d8715f5ebadd764466f7bde" FOREIGN KEY ("sellerChannelId") REFERENCES "channel" ("id") ON DELETE SET NULL ON UPDATE NO ACTION)`, undefined);
        await queryRunner.query(`INSERT INTO "order_line"("createdAt", "updatedAt", "quantity", "orderPlacedQuantity", "listPriceIncludesTax", "adjustments", "taxLines", "id", "sellerChannelId", "shippingLineId", "productVariantId", "taxCategoryId", "initialListPrice", "listPrice", "featuredAssetId", "orderId") SELECT "createdAt", "updatedAt", "quantity", "orderPlacedQuantity", "listPriceIncludesTax", "adjustments", "taxLines", "id", "sellerChannelId", "shippingLineId", "productVariantId", "taxCategoryId", "initialListPrice", "listPrice", "featuredAssetId", "orderId" FROM "temporary_order_line"`, undefined);
        await queryRunner.query(`DROP TABLE "temporary_order_line"`, undefined);
        await queryRunner.query(`CREATE INDEX "IDX_6901d8715f5ebadd764466f7bd" ON "order_line" ("sellerChannelId") `, undefined);
        await queryRunner.query(`CREATE INDEX "IDX_dc9ac68b47da7b62249886affb" ON "order_line" ("shippingLineId") `, undefined);
        await queryRunner.query(`CREATE INDEX "IDX_cbcd22193eda94668e84d33f18" ON "order_line" ("productVariantId") `, undefined);
        await queryRunner.query(`CREATE INDEX "IDX_77be94ce9ec650446617946227" ON "order_line" ("taxCategoryId") `, undefined);
        await queryRunner.query(`CREATE INDEX "IDX_9f065453910ea77d4be8e92618" ON "order_line" ("featuredAssetId") `, undefined);
        await queryRunner.query(`CREATE INDEX "IDX_239cfca2a55b98b90b6bef2e44" ON "order_line" ("orderId") `, undefined);
        await queryRunner.query(`DROP TABLE "decal"`, undefined);
   }

}
