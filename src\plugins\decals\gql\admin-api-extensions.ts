import gql from 'graphql-tag';

export const adminApiExtensions = gql`
    type Decal implements Node {
        id: ID!
        createdAt: DateTime!
        updatedAt: DateTime!
        code: String!
        name: String!
        description: String
        price: Money!
        colorOptions: [DecalColorOption!]!
        image: Asset
        enabled: Boolean!
    }

    type DecalColorOption {
        name: String!
        hexCode: String!
        priceModifier: Money!
    }

    type DecalList implements PaginatedList {
        items: [Decal!]!
        totalItems: Int!
    }

    input CreateDecalInput {
        code: String!
        name: String!
        description: String
        price: Money!
        colorOptions: [DecalColorOptionInput!]
        imageId: ID
        enabled: Boolean
    }

    input UpdateDecalInput {
        id: ID!
        code: String
        name: String
        description: String
        price: Money
        colorOptions: [DecalColorOptionInput!]
        imageId: ID
        enabled: Boolean
    }

    input DecalColorOptionInput {
        name: String!
        hexCode: String!
        priceModifier: Money!
    }

    input DecalListOptions {
        skip: Int
        take: Int
        sort: DecalSortParameter
        filter: DecalFilterParameter
    }

    input DecalSortParameter {
        id: SortOrder
        createdAt: SortOrder
        updatedAt: SortOrder
        code: SortOrder
        name: SortOrder
        price: SortOrder
        enabled: SortOrder
    }

    input DecalFilterParameter {
        id: IDOperators
        createdAt: DateOperators
        updatedAt: DateOperators
        code: StringOperators
        name: StringOperators
        description: StringOperators
        price: NumberOperators
        enabled: BooleanOperators
    }

    extend type Query {
        decal(id: ID!): Decal
        decals(options: DecalListOptions): DecalList!
    }

    extend type Mutation {
        createDecal(input: CreateDecalInput!): Decal!
        updateDecal(input: UpdateDecalInput!): Decal!
        deleteDecal(id: ID!): DeletionResponse!
        deleteDecals(ids: [ID!]!): [DeletionResponse!]!
    }
`;
