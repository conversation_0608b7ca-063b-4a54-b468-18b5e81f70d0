import { addNavMenuItem } from '@vendure/admin-ui/core';

export default [
    addNavMenuItem({
        id: 'decals',
        label: 'Decals',
        routerLink: ['/extensions/decals'],
        // Optionally, you can specify an icon and a section in the nav menu
        // icon: 'example-icon', // Replace with an actual icon class if desired
        // section: 'marketing', // Or any other existing or new section
    }, 
    // This second argument is the ID of the NavMenuSection to which this item should be added.
    // Common sections include 'catalog', 'sales', 'marketing', 'settings'.
    // If omitted, it defaults to the 'settings' section.
    'catalog' // For example, adding it to the 'catalog' section
    ),
];
