import gql from 'graphql-tag';

export const shopApiExtensions = gql`
    type Decal implements Node {
        id: ID!
        createdAt: DateTime!
        updatedAt: DateTime!
        code: String!
        name: String!
        description: String
        price: Money!
        colorOptions: [DecalColorOption!]!
        image: Asset
        enabled: Boolean!
    }

    type DecalColorOption {
        name: String!
        hexCode: String!
        priceModifier: Money!
    }

    extend type Query {
        availableDecals: [Decal!]!
        decal(id: ID!): Decal
    }
`;
