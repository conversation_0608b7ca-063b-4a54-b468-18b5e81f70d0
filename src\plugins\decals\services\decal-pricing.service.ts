import { Injectable } from '@nestjs/common';
import { RequestContext, TransactionalConnection } from '@vendure/core';
import { Decal } from '../entities/decal.entity';
import { SelectedDecal } from '../types';

@Injectable()
export class DecalPricingService {
    constructor(private connection: TransactionalConnection) {}

    /**
     * Calculate the total price for selected decals
     */
    async calculateDecalPrice(
        ctx: RequestContext,
        selectedDecals: SelectedDecal[]
    ): Promise<number> {
        if (!selectedDecals || selectedDecals.length === 0) {
            return 0;
        }

        let totalDecalPrice = 0;
        const decalIds = selectedDecals.map(sd => sd.decalId);

        if (decalIds.length > 0) {
            const decals = await this.connection
                .getRepository(ctx, Decal)
                .findByIds(decalIds);

            const decalMap = new Map(decals.map(d => [d.id.toString(), d]));

            for (const selectedDecal of selectedDecals) {
                const decal = decalMap.get(selectedDecal.decalId.toString());
                if (decal && decal.enabled) {
                    // Add base decal price (already in smallest currency unit)
                    totalDecalPrice += decal.price;

                    // Add color option price modifier if applicable
                    if (selectedDecal.colorOption) {
                        totalDecalPrice += selectedDecal.colorOption.priceModifier;
                    }
                }
            }
        }

        return totalDecalPrice;
    }

    /**
     * Parse selected decals from custom field JSON string
     */
    parseSelectedDecals(selectedDecalsString: string | SelectedDecal[]): SelectedDecal[] {
        if (!selectedDecalsString) {
            return [];
        }

        try {
            return typeof selectedDecalsString === 'string'
                ? JSON.parse(selectedDecalsString)
                : selectedDecalsString;
        } catch (error) {
            return [];
        }
    }

    /**
     * Calculate total item price including base price and decals
     */
    async calculateTotalItemPrice(
        ctx: RequestContext,
        basePrice: number,
        selectedDecalsString: string | SelectedDecal[]
    ): Promise<number> {
        const selectedDecals = this.parseSelectedDecals(selectedDecalsString);
        const decalPrice = await this.calculateDecalPrice(ctx, selectedDecals);
        return basePrice + decalPrice;
    }
} 