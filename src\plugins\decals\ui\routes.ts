import { registerRouteComponent } from '@vendure/admin-ui/core';
import { DecalListComponent } from './components/decal-list/decal-list.component';
import { DecalDetailComponent } from './components/decal-detail/decal-detail.component';

export default [
    registerRouteComponent({
        component: DecalListComponent,
        path: '',
        title: 'Decals',
        breadcrumb: 'Decals',
    }),
    registerRouteComponent({
        component: DecalDetailComponent,
        path: 'create',
        title: 'Create Decal',
        breadcrumb: [
            { label: 'Decals', link: ['/extensions', 'decals'] },
            { label: 'Create Decal', link: [] },
        ],
    }),
    registerRouteComponent({
        component: DecalDetailComponent,
        path: ':id',
        title: 'Edit Decal',
        breadcrumb: [
            { label: 'Decals', link: ['/extensions', 'decals'] },
            { label: '{{ entity.name }}', link: [] },
        ],
    }),
];
